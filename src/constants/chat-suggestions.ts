import { LucideIcon, ChartNoAxesCombined, Bolt, Globe, TrainTrack, ChartArea } from 'lucide-react'

export interface ChatSuggestion {
    id: number
    title: string
    content: string
    icon: LucideIcon
}

export const CHAT_SUGGESTIONS: ChatSuggestion[] = [
    {
        id: 1,
        title: 'KPI Monitoring',
        content: 'What is the current KPI status of this project for this week?',
        icon: ChartArea,
    },
    {
        id: 2,
        title: 'Insights',
        content: 'Give the project Insights',
        icon: ChartNoAxesCombined,
    },
    {
        id: 3,
        title: 'Sprint Planning',
        content: 'Create a sprint plan for the upcoming two weeks.',
        icon: Bolt,
    },
    {
        id: 4,
        title: 'Market Analysis',
        content: 'Provide a market analysis for the last quarter.',
        icon: Globe,
    },
    {
        id: 5,
        title: 'Progress Tracking',
        content: 'Update me on team performance',
        icon: TrainTrack,
    },
]
