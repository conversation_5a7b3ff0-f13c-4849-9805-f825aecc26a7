export type AcceptanceCriteriaItem = {
    id: number
    criteria: string
    is_checked: boolean
}

export type UserDetails = {
    id: number
    first_name: string
    last_name: string
    img_url: string | null
}[]

export type TaskDetails = {
    id: number
    title: string
    description: string
    short_code: string
    // Add other properties as needed
}[]
export type actor = {
    id: number
    first_name: string
    last_name: string
    img_url: string | null
}
export type ActivityLogType = {
    logTitle: string
    id: number
    entity_id: number
    entityType: string
    action: string
    description: string
    created_at: string
    created_by: {
        id: number
        first_name: string
        last_name: string
        img_url: string | null
    }
    actor: actor
    changes: {
        acceptance_criteria: {
            old: { id: number; criteria: string; is_checked: boolean }[]
            new: { id: number; criteria: string; is_checked: boolean }[]
        }
        content: {
            old: string
            new: string
        }
    }

    entityData: {
        id: number
        title: string
        short_code: string
        feat_code?: string
        name?: string
        details?: {
            userDetails: UserDetails
            taskDetails: TaskDetails
            id?: number
            title?: string
            short_code?: string
        }
        updatedAtributes?: {
            [key: string]: string | number
        }
        updatedTasks?: TaskDetails
    }
}

export type ChangeEntry =
    | { old: string | number | boolean | null; new: string | number | boolean | null }
    | { old: AcceptanceCriteriaItem[]; new: AcceptanceCriteriaItem[] }

// The full changes object
export type ActivityChanges = Record<string, ChangeEntry>
