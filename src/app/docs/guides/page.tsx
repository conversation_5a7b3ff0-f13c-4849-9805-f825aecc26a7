import Link from 'next/link'
import Image from 'next/image'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
    FileText,
    Settings,
    Lightbulb,
    HelpCircle,
    CheckCircle,
    ChevronRight,
    ArrowLeft,
    Clock,
    Users,
    BookOpen,
    Zap,
    Code,
    Search,
    ExternalLink,
    MessageCircle,
    Command,
} from 'lucide-react'

export default function GuidesPage() {
    return (
        <div className="min-h-screen bg-white">
            {/* SEO-Optimized Header */}
            <header className="border-b border-gray-200 sticky top-0 z-50 backdrop-blur-sm bg-white/95">
                <div className="w-full px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between h-16">
                        {/* Left Section - Logo and Navigation */}
                        <div className="flex items-center gap-3 sm:gap-6">
                            {/* Logo */}
                            <Link href="/" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
                                <Image
                                    src="/assets/img/raydian-logo.png"
                                    alt="Raydian.ai Logo"
                                    width={28}
                                    height={24}
                                    className="h-auto w-auto"
                                />
                                <span className="font-semibold text-base sm:text-lg text-[#18181B]">Raydian</span>
                            </Link>

                            {/* Breadcrumb */}
                            <div className="hidden md:flex items-center gap-2 text-sm text-gray-500">
                                <span>/</span>
                                <Link href="/docs" className="hover:text-gray-900 transition-colors">
                                    Documentation
                                </Link>
                                <span>/</span>
                                <span className="text-gray-900 font-medium">Guides</span>
                            </div>
                        </div>

                        {/* Center Section - Search (Desktop Only) */}
                        <div className="hidden sm:flex flex-1 max-w-lg mx-4">
                            <div className="relative w-full">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                <input
                                    type="text"
                                    placeholder="Search documentation..."
                                    className="w-full pl-10 pr-12 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 focus:bg-white transition-colors"
                                />
                                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
                                    <kbd className="hidden md:inline-flex items-center px-1.5 py-0.5 border border-gray-200 rounded text-xs font-mono text-gray-500">
                                        <Command className="h-3 w-3 mr-1" />K
                                    </kbd>
                                </div>
                            </div>
                        </div>

                        {/* Right Section - Actions */}
                        <div className="flex items-center gap-2 sm:gap-3">
                            {/* Mobile Search Button */}
                            <Button variant="ghost" size="sm" className="sm:hidden p-2 hover:bg-gray-100 rounded-md">
                                <Search className="h-4 w-4 text-gray-600" />
                                <span className="sr-only">Search</span>
                            </Button>

                            {/* GitHub Link */}
                            <Button variant="ghost" size="sm" asChild className="hidden md:flex">
                                <Link href="https://github.com" target="_blank" rel="noopener noreferrer">
                                    <ExternalLink className="h-4 w-4" />
                                    <span className="sr-only">GitHub</span>
                                </Link>
                            </Button>

                            {/* Support Link */}
                            <Button variant="ghost" size="sm" asChild className="hidden md:flex">
                                <Link href="/support" target="_blank" rel="noopener noreferrer">
                                    <MessageCircle className="h-4 w-4" />
                                    <span className="sr-only">Support</span>
                                </Link>
                            </Button>

                            {/* Get Started Button */}
                            <Button size="sm" asChild className="text-xs sm:text-sm">
                                <Link href="/sign-up">
                                    <span className="hidden sm:inline">Get Started</span>
                                    <span className="sm:hidden">Start</span>
                                    <ExternalLink className="ml-1 sm:ml-2 h-3 w-3" />
                                </Link>
                            </Button>
                        </div>
                    </div>
                </div>
            </header>

            <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
                <div className="flex gap-8">
                    {/* Left Sidebar */}
                    <aside className="hidden lg:block w-64 flex-shrink-0">
                        <div className="sticky top-24 max-h-screen overflow-y-auto">
                            <nav className="space-y-1">
                                <div className="pb-4">
                                    <h3 className="text-sm font-semibold text-gray-900 mb-3">Documentation</h3>
                                    <div className="space-y-1">
                                        <Link
                                            href="/docs"
                                            className="flex items-center gap-3 px-3 py-2.5 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors group">
                                            <BookOpen className="h-4 w-4" />
                                            <div>
                                                <div className="font-medium">Overview</div>
                                                <div className="text-xs text-gray-500">Documentation hub</div>
                                            </div>
                                        </Link>
                                        <Link
                                            href="/docs/getting-started"
                                            className="flex items-center gap-3 px-3 py-2.5 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors group">
                                            <Zap className="h-4 w-4" />
                                            <div>
                                                <div className="font-medium">Getting Started</div>
                                                <div className="text-xs text-gray-500">Quick setup guide</div>
                                            </div>
                                        </Link>
                                        <Link
                                            href="/docs/guides"
                                            className="flex items-center gap-3 px-3 py-2.5 text-sm text-blue-700 bg-blue-50 border border-blue-200 rounded-md">
                                            <FileText className="h-4 w-4" />
                                            <div>
                                                <div className="font-medium">How-to Guides</div>
                                                <div className="text-xs text-blue-600">Step-by-step tutorials</div>
                                            </div>
                                        </Link>
                                        <Link
                                            href="/docs/api"
                                            className="flex items-center gap-3 px-3 py-2.5 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors group">
                                            <Code className="h-4 w-4" />
                                            <div>
                                                <div className="font-medium">API Reference</div>
                                                <div className="text-xs text-gray-500">Developer documentation</div>
                                            </div>
                                        </Link>
                                    </div>
                                </div>

                                <div className="border-t border-gray-200 pt-4">
                                    <h3 className="text-sm font-semibold text-gray-900 mb-3">Guides</h3>
                                    <div className="space-y-1">
                                        <Link
                                            href="/docs/guides/creating-first-project"
                                            className="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors">
                                            Creating Your First Project
                                        </Link>
                                        <Link
                                            href="/docs/guides/advanced-planning"
                                            className="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors">
                                            Advanced Planning Features
                                        </Link>
                                        <Link
                                            href="/docs/guides/ai-prompt-optimization"
                                            className="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors">
                                            AI Prompt Optimization
                                        </Link>
                                        <Link
                                            href="/docs/guides/team-collaboration"
                                            className="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors">
                                            Team Collaboration
                                        </Link>
                                        <Link
                                            href="/docs/guides/troubleshooting"
                                            className="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors">
                                            Troubleshooting Guide
                                        </Link>
                                    </div>
                                </div>
                            </nav>
                        </div>
                    </aside>

                    {/* Main Content */}
                    <main className="flex-1 min-w-0">
                        <div className="max-w-4xl mx-auto">
                            {/* Breadcrumb Navigation */}
                            <nav className="mb-8">
                                <Link
                                    href="/docs"
                                    className="inline-flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 transition-colors">
                                    <ArrowLeft className="h-4 w-4" />
                                    Back to Documentation
                                </Link>
                            </nav>

                            {/* Page Header */}
                            <div className="mb-8">
                                <h1 className="text-4xl font-bold text-[#18181B] mb-4">How-to Guides</h1>
                                <p className="text-lg text-gray-600 leading-relaxed mb-6">
                                    Step-by-step tutorials to help you accomplish specific tasks with Raydian.ai. Master advanced
                                    features and optimization techniques with our comprehensive guides designed for project
                                    managers, team leads, and developers.
                                </p>
                                <p className="text-base text-gray-600 leading-relaxed">
                                    Each guide includes practical examples, best practices, and troubleshooting tips to help you
                                    get the most out of our AI-powered project planning platform.
                                </p>
                            </div>

                            {/* Guides Grid */}
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 mb-16">
                                <Card className="group border border-gray-200 shadow-sm rounded-2xl hover:shadow-lg hover:border-gray-300 transition-all duration-300 bg-white">
                                    <CardHeader className="pb-6 p-8">
                                        <div className="flex items-start justify-between mb-4">
                                            <div className="p-3 bg-gray-50 rounded-xl group-hover:bg-gray-100 transition-colors">
                                                <FileText className="h-6 w-6 text-gray-700" />
                                            </div>
                                            <div className="flex items-center gap-2 text-xs text-gray-500 bg-gray-50 px-3 py-1.5 rounded-full">
                                                <Clock className="h-3 w-3" />
                                                <span>10 min read</span>
                                            </div>
                                        </div>
                                        <CardTitle className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-gray-800 transition-colors">
                                            Creating Your First Project
                                        </CardTitle>
                                        <CardDescription className="text-base text-gray-600 leading-relaxed">
                                            Learn how to set up and configure your first project from scratch with our
                                            comprehensive step-by-step guide. Perfect for beginners getting started with
                                            Raydian.ai.
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="px-8 pb-8">
                                        <div className="space-y-4 mb-6">
                                            <h4 className="text-sm font-semibold text-gray-900 mb-3">What you&apos;ll learn:</h4>
                                            <div className="space-y-3">
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Project setup and initial configuration</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Setting clear goals and measurable objectives</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Inviting team members and assigning roles</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Best practices for project organization</span>
                                                </div>
                                            </div>
                                        </div>
                                        <Button
                                            className="w-full bg-gray-900 hover:bg-gray-800 text-white font-medium py-3 rounded-xl transition-colors"
                                            asChild>
                                            <Link href="/docs/guides/creating-first-project">
                                                Read Complete Guide <ChevronRight className="ml-2 h-4 w-4" />
                                            </Link>
                                        </Button>
                                    </CardContent>
                                </Card>

                                <Card className="group border border-gray-200 shadow-sm rounded-2xl hover:shadow-lg hover:border-gray-300 transition-all duration-300 bg-white">
                                    <CardHeader className="pb-6 p-8">
                                        <div className="flex items-start justify-between mb-4">
                                            <div className="p-3 bg-gray-50 rounded-xl group-hover:bg-gray-100 transition-colors">
                                                <Settings className="h-6 w-6 text-gray-700" />
                                            </div>
                                            <div className="flex items-center gap-2 text-xs text-gray-500 bg-gray-50 px-3 py-1.5 rounded-full">
                                                <Clock className="h-3 w-3" />
                                                <span>15 min read</span>
                                            </div>
                                        </div>
                                        <CardTitle className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-gray-800 transition-colors">
                                            Advanced Planning Features
                                        </CardTitle>
                                        <CardDescription className="text-base text-gray-600 leading-relaxed">
                                            Discover advanced features to simplify and optimize your project planning workflow
                                            with intelligent automation and powerful customization options.
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="px-8 pb-8">
                                        <div className="space-y-4 mb-6">
                                            <h4 className="text-sm font-semibold text-gray-900 mb-3">What you&apos;ll master:</h4>
                                            <div className="space-y-3">
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Custom templates and reusable workflows</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Automation rules and intelligent triggers</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Advanced analytics and custom reporting</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Integration with external tools and APIs</span>
                                                </div>
                                            </div>
                                        </div>
                                        <Button
                                            className="w-full bg-gray-900 hover:bg-gray-800 text-white font-medium py-3 rounded-xl transition-colors"
                                            asChild>
                                            <Link href="/docs/guides/advanced-planning">
                                                Read Complete Guide <ChevronRight className="ml-2 h-4 w-4" />
                                            </Link>
                                        </Button>
                                    </CardContent>
                                </Card>

                                <Card className="group border border-gray-200 shadow-sm rounded-2xl hover:shadow-lg hover:border-gray-300 transition-all duration-300 bg-white">
                                    <CardHeader className="pb-6 p-8">
                                        <div className="flex items-start justify-between mb-4">
                                            <div className="p-3 bg-gray-50 rounded-xl group-hover:bg-gray-100 transition-colors">
                                                <Lightbulb className="h-6 w-6 text-gray-700" />
                                            </div>
                                            <div className="flex items-center gap-2 text-xs text-gray-500 bg-gray-50 px-3 py-1.5 rounded-full">
                                                <Clock className="h-3 w-3" />
                                                <span>12 min read</span>
                                            </div>
                                        </div>
                                        <CardTitle className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-gray-800 transition-colors">
                                            AI Prompt Optimization
                                        </CardTitle>
                                        <CardDescription className="text-base text-gray-600 leading-relaxed">
                                            Learn how to write effective prompts to get the best results from our AI-powered
                                            planning system and maximize the quality of generated content.
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="px-8 pb-8">
                                        <div className="space-y-4 mb-6">
                                            <h4 className="text-sm font-semibold text-gray-900 mb-3">Key techniques:</h4>
                                            <div className="space-y-3">
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Writing clear and specific prompts</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Using context and practical examples</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Iterating and refining AI results</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Advanced prompt engineering strategies</span>
                                                </div>
                                            </div>
                                        </div>
                                        <Button
                                            className="w-full bg-gray-900 hover:bg-gray-800 text-white font-medium py-3 rounded-xl transition-colors"
                                            asChild>
                                            <Link href="/docs/guides/ai-prompt-optimization">
                                                Read Complete Guide <ChevronRight className="ml-2 h-4 w-4" />
                                            </Link>
                                        </Button>
                                    </CardContent>
                                </Card>

                                <Card className="group border border-gray-200 shadow-sm rounded-2xl hover:shadow-lg hover:border-gray-300 transition-all duration-300 bg-white">
                                    <CardHeader className="pb-6 p-8">
                                        <div className="flex items-start justify-between mb-4">
                                            <div className="p-3 bg-gray-50 rounded-xl group-hover:bg-gray-100 transition-colors">
                                                <Users className="h-6 w-6 text-gray-700" />
                                            </div>
                                            <div className="flex items-center gap-2 text-xs text-gray-500 bg-gray-50 px-3 py-1.5 rounded-full">
                                                <Clock className="h-3 w-3" />
                                                <span>8 min read</span>
                                            </div>
                                        </div>
                                        <CardTitle className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-gray-800 transition-colors">
                                            Team Collaboration
                                        </CardTitle>
                                        <CardDescription className="text-base text-gray-600 leading-relaxed">
                                            Best practices for collaborating with your team on projects and maximizing
                                            productivity through effective communication and workflow management.
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="px-8 pb-8">
                                        <div className="space-y-4 mb-6">
                                            <h4 className="text-sm font-semibold text-gray-900 mb-3">
                                                Collaboration essentials:
                                            </h4>
                                            <div className="space-y-3">
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Managing permissions and team roles</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Real-time collaboration features</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Communication and feedback tools</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Workflow optimization strategies</span>
                                                </div>
                                            </div>
                                        </div>
                                        <Button
                                            className="w-full bg-gray-900 hover:bg-gray-800 text-white font-medium py-3 rounded-xl transition-colors"
                                            asChild>
                                            <Link href="/docs/guides/team-collaboration">
                                                Read Complete Guide <ChevronRight className="ml-2 h-4 w-4" />
                                            </Link>
                                        </Button>
                                    </CardContent>
                                </Card>

                                <Card className="group border border-gray-200 shadow-sm rounded-2xl hover:shadow-lg hover:border-gray-300 transition-all duration-300 bg-white">
                                    <CardHeader className="pb-6 p-8">
                                        <div className="flex items-start justify-between mb-4">
                                            <div className="p-3 bg-gray-50 rounded-xl group-hover:bg-gray-100 transition-colors">
                                                <HelpCircle className="h-6 w-6 text-gray-700" />
                                            </div>
                                            <div className="flex items-center gap-2 text-xs text-gray-500 bg-gray-50 px-3 py-1.5 rounded-full">
                                                <Clock className="h-3 w-3" />
                                                <span>6 min read</span>
                                            </div>
                                        </div>
                                        <CardTitle className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-gray-800 transition-colors">
                                            Troubleshooting Guide
                                        </CardTitle>
                                        <CardDescription className="text-base text-gray-600 leading-relaxed">
                                            Common issues, solutions, and troubleshooting steps to resolve problems quickly and
                                            efficiently. Get back to productive work faster.
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="px-8 pb-8">
                                        <div className="space-y-4 mb-6">
                                            <h4 className="text-sm font-semibold text-gray-900 mb-3">Quick solutions for:</h4>
                                            <div className="space-y-3">
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Common error messages and fixes</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Performance optimization tips</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Support contact methods</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>System status and maintenance</span>
                                                </div>
                                            </div>
                                        </div>
                                        <Button
                                            className="w-full bg-gray-900 hover:bg-gray-800 text-white font-medium py-3 rounded-xl transition-colors"
                                            asChild>
                                            <Link href="/docs/guides/troubleshooting">
                                                Read Complete Guide <ChevronRight className="ml-2 h-4 w-4" />
                                            </Link>
                                        </Button>
                                    </CardContent>
                                </Card>

                                <Card className="group border border-gray-200 shadow-sm rounded-2xl hover:shadow-lg hover:border-gray-300 transition-all duration-300 bg-white">
                                    <CardHeader className="pb-6 p-8">
                                        <div className="flex items-start justify-between mb-4">
                                            <div className="p-3 bg-gray-50 rounded-xl group-hover:bg-gray-100 transition-colors">
                                                <Code className="h-6 w-6 text-gray-700" />
                                            </div>
                                            <div className="flex items-center gap-2 text-xs text-gray-500 bg-gray-50 px-3 py-1.5 rounded-full">
                                                <Clock className="h-3 w-3" />
                                                <span>20 min read</span>
                                            </div>
                                        </div>
                                        <CardTitle className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-gray-800 transition-colors">
                                            API Integration Guide
                                        </CardTitle>
                                        <CardDescription className="text-base text-gray-600 leading-relaxed">
                                            Complete guide to integrating Raydian.ai with your existing tools and workflows using
                                            our powerful REST API and webhooks.
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="px-8 pb-8">
                                        <div className="space-y-4 mb-6">
                                            <h4 className="text-sm font-semibold text-gray-900 mb-3">Integration topics:</h4>
                                            <div className="space-y-3">
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Authentication and API key setup</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Common integration patterns</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Code examples and SDKs</span>
                                                </div>
                                                <div className="flex items-start gap-3 text-sm text-gray-700">
                                                    <CheckCircle className="h-4 w-4 text-gray-600 flex-shrink-0 mt-0.5" />
                                                    <span>Webhook configuration and testing</span>
                                                </div>
                                            </div>
                                        </div>
                                        <Button
                                            className="w-full bg-gray-900 hover:bg-gray-800 text-white font-medium py-3 rounded-xl transition-colors"
                                            asChild>
                                            <Link href="/docs/api">
                                                View Complete API Docs <ChevronRight className="ml-2 h-4 w-4" />
                                            </Link>
                                        </Button>
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Help Section */}
                            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-[20px] p-6">
                                <h3 className="text-xl font-semibold text-[#18181B] mb-4">Need help with something specific?</h3>
                                <p className="text-base text-gray-600 mb-4">
                                    Can&apos;t find what you&apos;re looking for? Our support team is here to help you succeed
                                    with Raydian.ai.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-3">
                                    <Button variant="outline" asChild>
                                        <Link href="/support">Contact Support</Link>
                                    </Button>
                                    <Button variant="outline" asChild>
                                        <Link href="/docs/api">API Documentation</Link>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </main>

                    {/* Right Sidebar */}
                    <aside className="hidden xl:block w-56 flex-shrink-0">
                        <div className="sticky top-24 max-h-screen overflow-y-auto">
                            <nav className="space-y-6">
                                <div>
                                    <h3 className="text-xs font-semibold text-gray-900 uppercase tracking-wide mb-3">
                                        Quick Navigation
                                    </h3>
                                    <div className="space-y-2">
                                        <Link href="/docs/guides" className="block text-xs text-blue-600 font-medium">
                                            Guides
                                        </Link>
                                        <Link
                                            href="/docs/guides"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Tutorials
                                        </Link>
                                        <Link
                                            href="/docs/guides"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            How-to guides
                                        </Link>
                                        <Link
                                            href="/docs/api"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Integrations
                                        </Link>
                                    </div>
                                </div>

                                <div className="border-t border-gray-200 pt-4">
                                    <h3 className="text-xs font-semibold text-gray-900 uppercase tracking-wide mb-3">
                                        Developer Resources
                                    </h3>
                                    <div className="space-y-2">
                                        <Link
                                            href="/docs/api"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            API Reference
                                        </Link>
                                        <Link
                                            href="/docs/guides"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Additional Resources
                                        </Link>
                                        <Link
                                            href="/docs/api"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Versions
                                        </Link>
                                    </div>
                                </div>

                                <div className="border-t border-gray-200 pt-4">
                                    <h3 className="text-xs font-semibold text-gray-900 uppercase tracking-wide mb-3">
                                        Community
                                    </h3>
                                    <div className="space-y-2">
                                        <Link
                                            href="/docs/guides/troubleshooting"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Security
                                        </Link>
                                        <Link
                                            href="/support"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Contributing
                                        </Link>
                                    </div>
                                </div>

                                <div className="border-t border-gray-200 pt-4">
                                    <h3 className="text-xs font-semibold text-gray-900 uppercase tracking-wide mb-3">
                                        Popular Guides
                                    </h3>
                                    <div className="space-y-2">
                                        <Link
                                            href="/docs/guides/creating-first-project"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Creating First Project
                                        </Link>
                                        <Link
                                            href="/docs/guides/ai-prompt-optimization"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            AI Prompt Optimization
                                        </Link>
                                        <Link
                                            href="/docs/guides/team-collaboration"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Team Collaboration
                                        </Link>
                                        <Link
                                            href="/docs/guides/troubleshooting"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Troubleshooting
                                        </Link>
                                    </div>
                                </div>
                            </nav>
                        </div>
                    </aside>
                </div>
            </div>
        </div>
    )
}
