import type { Metadata } from 'next'

export const metadata: Metadata = {
    title: 'Getting Started - Raydian.ai Documentation',
    description:
        'Learn how to get started with Raydian.ai in 3 simple steps. Complete guide to account setup, project creation, and AI-powered planning for project managers and teams.',
    keywords:
        'Raydian.ai getting started, AI project management setup, create account, first project, project planning tutorial, onboarding guide',
    openGraph: {
        title: 'Getting Started with Raydian.ai - Complete Setup Guide',
        description:
            'Learn how to get started with Raydian.ai in 3 simple steps. Complete guide to account setup, project creation, and AI-powered planning.',
        type: 'article',
        url: 'https://raydian.ai/docs/getting-started',
        siteName: 'Raydian.ai',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Getting Started with Raydian.ai - Complete Setup Guide',
        description:
            'Learn how to get started with Raydian.ai in 3 simple steps. Complete guide to account setup, project creation, and AI-powered planning.',
    },
    alternates: {
        canonical: 'https://raydian.ai/docs/getting-started',
    },
    robots: {
        index: true,
        follow: true,
        googleBot: {
            index: true,
            follow: true,
            'max-video-preview': -1,
            'max-image-preview': 'large',
            'max-snippet': -1,
        },
    },
}

export default function GettingStartedLayout({ children }: { children: React.ReactNode }) {
    const jsonLd = {
        '@context': 'https://schema.org',
        '@type': 'HowTo',
        name: 'How to Get Started with Raydian.ai',
        description: 'Complete step-by-step guide to getting started with Raydian.ai AI-powered project planning platform',
        image: 'https://raydian.ai/assets/img/getting-started-guide.jpg',
        totalTime: 'PT10M',
        estimatedCost: {
            '@type': 'MonetaryAmount',
            currency: 'USD',
            value: '0',
        },
        supply: [
            {
                '@type': 'HowToSupply',
                name: 'Email address',
            },
            {
                '@type': 'HowToSupply',
                name: 'Strong password',
            },
        ],
        tool: [
            {
                '@type': 'HowToTool',
                name: 'Web browser',
            },
            {
                '@type': 'HowToTool',
                name: 'Internet connection',
            },
        ],
        step: [
            {
                '@type': 'HowToStep',
                name: 'Create Your Account',
                text: 'Sign up for a free Raydian.ai account with email verification and optional 2FA setup',
                image: 'https://raydian.ai/assets/img/step-1-account.jpg',
                url: 'https://raydian.ai/docs/getting-started#step-1',
            },
            {
                '@type': 'HowToStep',
                name: 'Set Up Your First Project',
                text: 'Create your first project with clear goals, timeline, and requirements for AI analysis',
                image: 'https://raydian.ai/assets/img/step-2-project.jpg',
                url: 'https://raydian.ai/docs/getting-started#step-2',
            },
            {
                '@type': 'HowToStep',
                name: 'Start AI-Powered Planning',
                text: 'Use AI tools to break down your project into tasks with timeline and resource recommendations',
                image: 'https://raydian.ai/assets/img/step-3-planning.jpg',
                url: 'https://raydian.ai/docs/getting-started#step-3',
            },
        ],
        author: {
            '@type': 'Organization',
            name: 'Raydian.ai',
            url: 'https://raydian.ai',
        },
        publisher: {
            '@type': 'Organization',
            name: 'Raydian.ai',
            url: 'https://raydian.ai',
            logo: {
                '@type': 'ImageObject',
                url: 'https://raydian.ai/assets/img/raydian-logo.png',
            },
        },
        datePublished: '2024-01-01',
        dateModified: new Date().toISOString(),
        mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': 'https://raydian.ai/docs/getting-started',
        },
    }

    return (
        <>
            <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} />
            {children}
        </>
    )
}
