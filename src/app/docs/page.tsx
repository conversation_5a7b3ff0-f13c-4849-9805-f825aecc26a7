'use client'

import Link from 'next/link'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
    BookOpen,
    HelpCircle,
    Settings,
    ChevronRight,
    FileText,
    Lightbulb,
    ArrowRight,
    Search,
    ExternalLink,
    MessageCircle,
    Command,
    Clock,
    Users,
    Code,
    Zap,
} from 'lucide-react'

export default function DocsPage() {
    return (
        <div className="min-h-screen bg-white">
            {/* SEO-Optimized Header */}
            <header className="border-b border-gray-200 sticky top-0 z-50 backdrop-blur-sm bg-white/95">
                <div className="w-full px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between h-16">
                        {/* Left Section - Logo and Navigation */}
                        <div className="flex items-center gap-3 sm:gap-6">
                            {/* Logo */}
                            <Link href="/" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
                                <Image
                                    src="/assets/img/raydian-logo.png"
                                    alt="Raydian.ai Logo"
                                    width={28}
                                    height={24}
                                    className="h-auto w-auto"
                                />
                                <span className="font-semibold text-base sm:text-lg text-[#18181B]">Raydian</span>
                            </Link>

                            {/* Breadcrumb */}
                            <div className="hidden md:flex items-center gap-2 text-sm text-gray-500">
                                <span>/</span>
                                <span className="text-gray-900 font-medium">Documentation</span>
                            </div>
                        </div>

                        {/* Center Section - Search (Desktop Only) */}
                        <div className="hidden sm:flex flex-1 max-w-lg mx-4">
                            <div className="relative w-full">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                <input
                                    type="text"
                                    placeholder="Search documentation..."
                                    className="w-full pl-10 pr-12 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 focus:bg-white transition-colors"
                                />
                                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
                                    <kbd className="hidden md:inline-flex items-center px-1.5 py-0.5 border border-gray-200 rounded text-xs font-mono text-gray-500">
                                        <Command className="h-3 w-3 mr-1" />K
                                    </kbd>
                                </div>
                            </div>
                        </div>

                        {/* Right Section - Actions */}
                        <div className="flex items-center gap-2 sm:gap-3">
                            {/* Mobile Search Button */}
                            <Button variant="ghost" size="sm" className="sm:hidden p-2 hover:bg-gray-100 rounded-md">
                                <Search className="h-4 w-4 text-gray-600" />
                                <span className="sr-only">Search</span>
                            </Button>

                            {/* GitHub Link */}
                            <Button variant="ghost" size="sm" asChild className="hidden md:flex">
                                <Link href="https://github.com" target="_blank" rel="noopener noreferrer">
                                    <ExternalLink className="h-4 w-4" />
                                    <span className="sr-only">GitHub</span>
                                </Link>
                            </Button>

                            {/* Support Link */}
                            <Button variant="ghost" size="sm" asChild className="hidden md:flex">
                                <Link href="/support" target="_blank" rel="noopener noreferrer">
                                    <MessageCircle className="h-4 w-4" />
                                    <span className="sr-only">Support</span>
                                </Link>
                            </Button>

                            {/* Get Started Button */}
                            <Button size="sm" asChild className="text-xs sm:text-sm">
                                <Link href="/sign-up">
                                    <span className="hidden sm:inline">Get Started</span>
                                    <span className="sm:hidden">Start</span>
                                    <ExternalLink className="ml-1 sm:ml-2 h-3 w-3" />
                                </Link>
                            </Button>
                        </div>
                    </div>
                </div>
            </header>

            <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
                <div className="flex gap-8">
                    {/* Left Sidebar */}
                    <aside className="hidden lg:block w-64 flex-shrink-0">
                        <div className="sticky top-24 max-h-screen overflow-y-auto">
                            <nav className="space-y-1">
                                <div className="pb-4">
                                    <h3 className="text-sm font-semibold text-gray-900 mb-3">Documentation</h3>
                                    <div className="space-y-1">
                                        <Link
                                            href="/docs"
                                            className="flex items-center gap-3 px-3 py-2.5 text-sm text-blue-700 bg-blue-50 border border-blue-200 rounded-md">
                                            <BookOpen className="h-4 w-4" />
                                            <div>
                                                <div className="font-medium">Overview</div>
                                                <div className="text-xs text-blue-600">Documentation hub</div>
                                            </div>
                                        </Link>
                                        <Link
                                            href="/docs/getting-started"
                                            className="flex items-center gap-3 px-3 py-2.5 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors group">
                                            <Zap className="h-4 w-4" />
                                            <div>
                                                <div className="font-medium">Getting Started</div>
                                                <div className="text-xs text-gray-500">Quick setup guide</div>
                                            </div>
                                        </Link>
                                        <Link
                                            href="/docs/guides"
                                            className="flex items-center gap-3 px-3 py-2.5 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors group">
                                            <FileText className="h-4 w-4" />
                                            <div>
                                                <div className="font-medium">How-to Guides</div>
                                                <div className="text-xs text-gray-500">Step-by-step tutorials</div>
                                            </div>
                                        </Link>
                                    </div>
                                </div>

                                <div className="border-t border-gray-200 pt-4">
                                    <h3 className="text-sm font-semibold text-gray-900 mb-3">Popular Guides</h3>
                                    <div className="space-y-1">
                                        <Link
                                            href="/docs/guides/creating-first-project"
                                            className="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors">
                                            Creating Your First Project
                                        </Link>
                                        <Link
                                            href="/docs/guides/ai-prompt-optimization"
                                            className="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors">
                                            AI Prompt Optimization
                                        </Link>
                                        <Link
                                            href="/docs/guides/team-collaboration"
                                            className="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors">
                                            Team Collaboration
                                        </Link>
                                        <Link
                                            href="/docs/guides/troubleshooting"
                                            className="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors">
                                            Troubleshooting
                                        </Link>
                                    </div>
                                </div>
                            </nav>
                        </div>
                    </aside>

                    {/* Main Content */}
                    <main className="flex-1 min-w-0">
                        <div className="max-w-4xl mx-auto">
                            {/* Hero Section */}
                            <div className="text-center mb-16">
                                <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Raydian.ai Documentation</h1>
                                <p className="text-lg text-gray-600 leading-relaxed mb-8 max-w-2xl mx-auto">
                                    Everything you need to master AI-powered project planning. From quick setup to advanced
                                    features, our comprehensive guides help you transform your workflow and achieve better project
                                    outcomes.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                                    <Button size="lg" className="bg-gray-900 hover:bg-gray-800" asChild>
                                        <Link href="/docs/getting-started">
                                            Get Started <ArrowRight className="ml-2 h-4 w-4" />
                                        </Link>
                                    </Button>
                                    <Button variant="outline" size="lg" className="border-gray-300 hover:bg-gray-50" asChild>
                                        <Link href="/docs/guides">Browse Guides</Link>
                                    </Button>
                                </div>
                            </div>

                            {/* Main Navigation Cards */}
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-16">
                                <Card className="group border py-2 border-gray-200 shadow-sm rounded-xl hover:shadow-md hover:border-gray-300 transition-all duration-300 bg-white">
                                    <CardContent className="p-4">
                                        <div className="flex flex-col gap-4">
                                            <div className="flex items-center gap-4">
                                                <div className="p-2.5 bg-gray-50 rounded-lg group-hover:bg-gray-100 transition-colors flex-shrink-0">
                                                    <Zap className="h-5 w-5 text-gray-700" />
                                                </div>
                                                <div className="flex items-center gap-3">
                                                    <h3 className="text-lg font-semibold text-gray-900">Quick Start</h3>
                                                    <div className="flex items-center gap-1 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                                        <Clock className="h-3 w-3" />
                                                        <span>10 min</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <p className="text-sm text-gray-600 leading-relaxed">
                                                Get up and running with Raydian.ai in minutes. Complete setup guide from account
                                                creation to your first AI-powered project plan.
                                            </p>
                                            <Button className="bg-gray-900 hover:bg-gray-800 w-full" asChild>
                                                <Link href="/docs/getting-started">
                                                    Start Here <ArrowRight className="ml-2 h-4 w-4" />
                                                </Link>
                                            </Button>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card className="group border py-2 border-gray-200 shadow-sm rounded-xl hover:shadow-md hover:border-gray-300 transition-all duration-300 bg-white">
                                    <CardContent className="p-4">
                                        <div className="flex flex-col gap-4">
                                            <div className="flex items-center gap-4">
                                                <div className="p-2.5 bg-gray-50 rounded-lg group-hover:bg-gray-100 transition-colors flex-shrink-0">
                                                    <BookOpen className="h-5 w-5 text-gray-700" />
                                                </div>
                                                <div className="flex items-center gap-3">
                                                    <h3 className="text-lg font-semibold text-gray-900">How-to Guides</h3>
                                                    <div className="flex items-center gap-1 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                                        <FileText className="h-3 w-3" />
                                                        <span>6 guides</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <p className="text-sm text-gray-600 leading-relaxed">
                                                Step-by-step tutorials for specific tasks. Master advanced features, optimization
                                                techniques, and best practices.
                                            </p>
                                            <Button variant="outline" className="border-gray-300 hover:bg-gray-50 w-full" asChild>
                                                <Link href="/docs/guides">
                                                    Browse Guides <ChevronRight className="ml-2 h-4 w-4" />
                                                </Link>
                                            </Button>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Popular Topics */}
                            <div className="mb-12">
                                <h2 className="text-3xl font-semibold text-[#18181B] mb-6">Popular Topics</h2>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="space-y-4">
                                        <Link
                                            href="/docs/guides/creating-first-project"
                                            className="block p-4 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50/50 transition-all">
                                            <div className="flex items-center gap-3">
                                                <FileText className="h-5 w-5 text-blue-600" />
                                                <div>
                                                    <h3 className="font-semibold text-gray-900">Creating Your First Project</h3>
                                                    <p className="text-sm text-gray-600">Complete project setup guide</p>
                                                </div>
                                            </div>
                                        </Link>
                                        <Link
                                            href="/docs/guides/ai-prompt-optimization"
                                            className="block p-4 rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50/50 transition-all">
                                            <div className="flex items-center gap-3">
                                                <Lightbulb className="h-5 w-5 text-purple-600" />
                                                <div>
                                                    <h3 className="font-semibold text-gray-900">AI Prompt Optimization</h3>
                                                    <p className="text-sm text-gray-600">Get better AI results</p>
                                                </div>
                                            </div>
                                        </Link>
                                        <Link
                                            href="/docs/guides/team-collaboration"
                                            className="block p-4 rounded-lg border border-gray-200 hover:border-orange-300 hover:bg-orange-50/50 transition-all">
                                            <div className="flex items-center gap-3">
                                                <Users className="h-5 w-5 text-orange-600" />
                                                <div>
                                                    <h3 className="font-semibold text-gray-900">Team Collaboration</h3>
                                                    <p className="text-sm text-gray-600">Work together effectively</p>
                                                </div>
                                            </div>
                                        </Link>
                                    </div>
                                    <div className="space-y-4">
                                        <Link
                                            href="/docs/guides/advanced-planning"
                                            className="block p-4 rounded-lg border border-gray-200 hover:border-green-300 hover:bg-green-50/50 transition-all">
                                            <div className="flex items-center gap-3">
                                                <Settings className="h-5 w-5 text-green-600" />
                                                <div>
                                                    <h3 className="font-semibold text-gray-900">Advanced Planning Features</h3>
                                                    <p className="text-sm text-gray-600">Unlock powerful capabilities</p>
                                                </div>
                                            </div>
                                        </Link>
                                        <Link
                                            href="/docs/guides/troubleshooting"
                                            className="block p-4 rounded-lg border border-gray-200 hover:border-red-300 hover:bg-red-50/50 transition-all">
                                            <div className="flex items-center gap-3">
                                                <HelpCircle className="h-5 w-5 text-red-600" />
                                                <div>
                                                    <h3 className="font-semibold text-gray-900">Troubleshooting</h3>
                                                    <p className="text-sm text-gray-600">Solve common issues</p>
                                                </div>
                                            </div>
                                        </Link>
                                        <Link
                                            href="/docs/api"
                                            className="block p-4 rounded-lg border border-gray-200 hover:border-indigo-300 hover:bg-indigo-50/50 transition-all">
                                            <div className="flex items-center gap-3">
                                                <Code className="h-5 w-5 text-indigo-600" />
                                                <div>
                                                    <h3 className="font-semibold text-gray-900">API Integration</h3>
                                                    <p className="text-sm text-gray-600">Connect with your tools</p>
                                                </div>
                                            </div>
                                        </Link>
                                    </div>
                                </div>
                            </div>

                            {/* Help Section */}
                            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-[20px] p-8 text-center">
                                <h3 className="text-2xl font-semibold text-[#18181B] mb-4">Need Help?</h3>
                                <p className="text-lg text-gray-600 mb-6 max-w-2xl mx-auto">
                                    Can&apos;t find what you&apos;re looking for? Our support team is here to help you succeed
                                    with Raydian.ai.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <Button variant="outline" size="lg" asChild>
                                        <Link href="/support">Contact Support</Link>
                                    </Button>
                                    <Button variant="outline" size="lg" asChild>
                                        <Link href="https://github.com" target="_blank" rel="noopener noreferrer">
                                            GitHub Discussions
                                        </Link>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </main>

                    {/* Right Sidebar */}
                    <aside className="hidden xl:block w-56 flex-shrink-0">
                        <div className="sticky top-24 max-h-screen overflow-y-auto">
                            <nav className="space-y-6">
                                <div>
                                    <h3 className="text-xs font-semibold text-gray-900 uppercase tracking-wide mb-3">
                                        Quick Navigation
                                    </h3>
                                    <div className="space-y-2">
                                        <Link
                                            href="/docs/guides"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Guides
                                        </Link>
                                        <Link
                                            href="/docs/guides"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Tutorials
                                        </Link>
                                        <Link
                                            href="/docs/guides"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            How-to guides
                                        </Link>
                                        <Link
                                            href="/docs/api"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Integrations
                                        </Link>
                                    </div>
                                </div>

                                <div className="border-t border-gray-200 pt-4">
                                    <h3 className="text-xs font-semibold text-gray-900 uppercase tracking-wide mb-3">
                                        Community
                                    </h3>
                                    <div className="space-y-2">
                                        <Link
                                            href="/docs/guides/troubleshooting"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Security
                                        </Link>
                                        <Link
                                            href="/support"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Contributing
                                        </Link>
                                    </div>
                                </div>

                                <div className="border-t border-gray-200 pt-4">
                                    <h3 className="text-xs font-semibold text-gray-900 uppercase tracking-wide mb-3">
                                        Version Info
                                    </h3>
                                    <div className="space-y-2">
                                        {/* <div className="text-xs text-gray-600">Current: v2.1.0</div> */}
                                        <div className="text-xs text-gray-600">Current: v0.1.0</div>
                                        <Link
                                            href="/docs/api"
                                            className="block text-xs text-blue-600 hover:text-blue-800 transition-colors">
                                            View changelog
                                        </Link>
                                    </div>
                                </div>

                                <div className="border-t border-gray-200 pt-4">
                                    <h3 className="text-xs font-semibold text-gray-900 uppercase tracking-wide mb-3">
                                        Help Section
                                    </h3>
                                    <div className="space-y-2">
                                        <Link
                                            href="/support"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Contact Support
                                        </Link>
                                        <Link
                                            href="/docs/guides/troubleshooting"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Report Issues
                                        </Link>
                                        <Link
                                            href="https://github.com"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            GitHub
                                        </Link>
                                    </div>
                                </div>
                            </nav>
                        </div>
                    </aside>
                </div>
            </div>
        </div>
    )
}
