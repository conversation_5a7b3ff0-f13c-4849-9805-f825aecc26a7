import SiteFooter from '@/app/(site)/site-components/site-footer'
import { Navbar } from '@/components/ui/navbar'
import type { Metadata } from 'next'

export const metadata: Metadata = {
    description:
        'AI-powered project planning tool that transforms your ideas into structured workflows. Start conversations, generate plans instantly, and execute with perfect clarity.',
    keywords: 'AI project management, task planning, workflow automation, project planning tool',
}

export default function Layout({ children }: { children: React.ReactNode }) {
    const jsonLd = {
        '@context': 'https://schema.org',
        '@type': 'SoftwareApplication',
        name: 'Raydian.ai',
        description: 'AI-powered project planning tool that transforms your ideas into structured workflows',
        url: 'https://raydian.ai',
        applicationCategory: 'BusinessApplication',
        operatingSystem: 'Web',
        aggregateRating: {
            '@type': 'AggregateRating',
            ratingValue: '4.8',
            ratingCount: '100',
        },
        offers: {
            '@type': 'Offer',
            price: '0',
            priceCurrency: 'USD',
            description: 'Free tier with 100 prompts included',
        },
        featureList: [
            'AI-powered project planning',
            'Automated task generation',
            'Collaborative workspace',
            'Timeline management',
        ],
        creator: {
            '@type': 'Organization',
            name: 'Raydian.ai',
            url: 'https://raydian.ai',
        },
    }
    return (
        <>
            <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} />
            <div className="mb-0 md:mb-2 bg-white">
                <Navbar />
                {children}
                <SiteFooter />
            </div>
        </>
    )
}
