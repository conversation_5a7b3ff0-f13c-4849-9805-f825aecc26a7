'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Mail } from 'lucide-react'
import Image from 'next/image'
import { useState } from 'react'
import { z } from 'zod'
import { useJoinWaitingList } from '@/services/waiting-list.services'

// Zod schema for email validation
const emailSchema = z.object({
    email: z
        .string()
        .min(1, 'Email is required')
        .email('Please enter a valid email address')
        .max(100, 'Email must be less than 100 characters'),
})

export default function JoinWaitingList() {
    const [email, setEmail] = useState('')
    const [validationError, setValidationError] = useState<string | null>(null)
    const [honeypotInput, setHoneypotInput] = useState('')

    const submitMutation = useJoinWaitingList({
        onSuccess: () => {
            setEmail('')
            setValidationError(null)
        },
    })

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        if (honeypotInput) return
        // Clear previous validation error
        setValidationError(null)

        // Validate email using Zod
        const validationResult = emailSchema.safeParse({ email })

        if (!validationResult.success) {
            // Extract the first validation error
            const firstError = validationResult.error.errors[0]
            setValidationError(firstError.message)
            return
        }

        // Submit if validation passes
        submitMutation.mutate(validationResult.data.email)
    }

    const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setEmail(e.target.value)
        // Clear validation error when user starts typing
        if (validationError) {
            setValidationError(null)
        }
    }

    return (
        <div className="relative h-[360px] lg:h-[608px] md:rounded-[20px] flex items-center justify-center overflow-hidden md:mx-2 lg:mx-8 md:mb-2">
            {/* Background Image */}
            <Image
                src={'/assets/img/join-waiting-list-bg.png'}
                fill
                priority
                alt="Join Waiting List Background"
                className="object-cover h-auto w-auto"
                quality={90}
            />

            {/* Content */}
            <div className="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
                <h4 className="text-[22px] lg:text-[50px] sm:text-3xl lg:text-4xl font-normal font-serif text-white mb-6 leading-tight lg:max-w-3xl">
                    Your future teammate runs on AI. Join the waitlist and meet Raydian first.
                </h4>

                <p className="text-[13px] lg:text-[16px] text-gray-200 mb-8 max-w-lg mx-auto">
                    We help your team plan better, move faster, and stay in sync — all with the power of agentic AI. Early access
                    gets you ahead of the curve.
                </p>

                {/* Email Signup Form */}
                <form onSubmit={handleSubmit} className="w-full lg:max-w-[612px] mx-auto">
                    <div className="flex gap-2 w-full">
                        <div className="relative flex-1">
                            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                            <Input
                                type="email"
                                placeholder="Your mail address"
                                className={`pl-10 h-12 bg-white border-gray-200 text-gray-900 placeholder:text-gray-500 ${
                                    validationError ? 'border-red-500 focus:border-red-500' : ''
                                }`}
                                name="email"
                                value={email}
                                onChange={handleEmailChange}
                            />
                            <input
                                type="text"
                                name="honeypot"
                                value={honeypotInput}
                                onChange={(e) => setHoneypotInput(e.target.value)}
                                className="hidden"
                            />
                        </div>
                        <Button
                            type="submit"
                            size="lg"
                            className="h-12 px-4 lg:px-8 text-[13px] lg:text-[16px] bg-emerald-600 hover:bg-emerald-700 text-white font-semibold whitespace-nowrap disabled:opacity-50"
                            disabled={submitMutation.isPending}>
                            {submitMutation.isPending ? 'subscribing...' : 'Subscribe'}
                        </Button>
                    </div>

                    {/* Validation Error */}
                    {validationError && <p className="text-red-400 text-sm ml-2 mt-2 text-left max-w-md">{validationError}</p>}
                </form>
            </div>
        </div>
    )
}
