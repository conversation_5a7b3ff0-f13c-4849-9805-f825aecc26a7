'use client'
import { But<PERSON> } from '@/components/ui/button'
import { CirclePlay } from 'lucide-react'
import Image from 'next/image'
import { motion, useScroll, useTransform } from 'framer-motion'
import { useRef } from 'react'
import NavigateToSignIn from './site-components/navigate-to-sign-in'

const Hero = () => {
    const imageRef = useRef<HTMLDivElement>(null)
    const { scrollYProgress } = useScroll({
        target: imageRef,
        offset: ['start end', 'end start'],
    })

    const rotateX = useTransform(scrollYProgress, [0, 0.33], [25, 0])
    const scale = useTransform(scrollYProgress, [0, 0.33], [0.9, 1])

    return (
        <section
            className="w-full   flex flex-col justify-center items-center gap-4 sm:gap-6 lg:gap-8 mt-6 sm:mt-8 lg:mt-10 3xl:mt-15 relative overflow-hidden"
            aria-labelledby="hero-heading"
            id="hero">
            <div className="w-full max-w-5xl 3xl:max-w-[1500px] mx-auto text-center px-4 sm:px-6 lg:px-8 relative z-10">
                <h1
                    id="hero-heading"
                    className="text-[27px] sm:text-4xl md:text-6xl lg:text-5xl xl:text-7xl 3xl:text-[96px] text-center font-serif font-medium text-[#18181B] leading-tight mb-4 sm:mb-6">
                    {/* The Shortest Path From &apos;What If ?&apos; to &apos;Done&apos;. */}
                    Project Management Made Easy.
                </h1>
                <p className="text-sm sm:text-base md:text-md lg:text-lg xl:text-xl 3xl:text-[24px] font-medium text-[#000000] max-w-4xl mx-auto leading-relaxed">
                    Raydian is the AI co-pilot that translates your chaotic brilliance into a crystal-clear plan. Stop wrestling
                    with the process and start building the future.
                </p>
                <div className="flex mx-auto w-fit gap-2 mt-8">
                    <NavigateToSignIn>
                        <Button className=" bg-[#08B38B] hover:bg-[#0c765e]" aria-label="Try Raydian project planning tool ">
                            Get Started.
                        </Button>
                    </NavigateToSignIn>
                    <Button
                        className=" bg-[#08B38B1C] hover:bg-[#0358441c] text-[#08B38B] hidden lg:flex"
                        aria-label="Try Raydian project planning tool ">
                        <CirclePlay className="h-4 w-4" /> View Demo
                    </Button>
                </div>
            </div>

            {/* Background gradient - optimized with priority and sizes */}
            <Image
                src="/assets/img/raydian-site-bg-hero-gradient.webp"
                fill
                alt="Raydian AI project planning interface"
                className="object-cover"
                priority
                sizes="100vw"
                quality={85}
                style={{ zIndex: 1 }}
            />

            {/* Hero planner image - responsive with proper breakpoints */}
            <motion.div
                ref={imageRef}
                className="relative w-full max-w-7xl mx-auto mt-8 sm:mt-12 lg:mt-16 xl:mt-2 px-4 sm:px-6 lg:px-8 pb-16"
                style={{
                    zIndex: 2,
                    rotateX,
                    scale,
                    transformPerspective: 1000,
                }}>
                <Image
                    src="/assets/img/hero-planner-image.webp"
                    width={1300}
                    height={800}
                    alt="Raydian AI project planning interface showing structured workflow creation"
                    className="w-full h-auto object-contain rounded-lg "
                    priority
                    sizes="(max-width: 640px) 100vw, (max-width: 1024px) 90vw, (max-width: 1280px) 85vw, 1200px"
                    quality={90}
                />
            </motion.div>
        </section>
    )
}

export default Hero
