import Image from 'next/image'
import PricingSection from '../pricing'
import Faq from '../faq'

const Pricing = () => {
    return (
        <section
            className="w-full flex flex-col justify-center items-center gap-4 sm:gap-6 lg:gap-8  relative overflow-hidden"
            aria-labelledby="pricing"
            id="pricing">
            <div className="w-full max-w-5xl 3xl:max-w-[1500px] mx-auto text-center px-4 sm:px-6 lg:px-8 relative z-10">
                <h1
                    id="pricing-heading"
                    className="text-[27px] sm:text-4xl md:text-6xl lg:text-5xl xl:text-7xl 3xl:text-[96px] text-center font-serif font-medium text-[#18181B] leading-tight mb-4 sm:mb-6">
                    One Simple Model. No Limits, Just Freedom.
                </h1>
                <p className="text-sm sm:text-base md:text-md lg:text-lg xl:text-xl 3xl:text-[24px] font-medium text-[#000000] max-w-4xl mx-auto leading-relaxed">
                    Enjoy all features free. Your first 100 prompts are on us. Upgrade only when you&apos;re ready to go further.
                </p>
            </div>

            {/* Background gradient - optimized with priority and sizes */}
            <Image
                src="/assets/img/raydian-site-bg-hero-gradient.webp"
                fill
                alt=""
                className="object-cover"
                priority
                sizes="100vw"
                quality={85}
                style={{ zIndex: 1 }}
            />
            <PricingSection />
            <Faq />
        </section>
    )
}

export default Pricing
