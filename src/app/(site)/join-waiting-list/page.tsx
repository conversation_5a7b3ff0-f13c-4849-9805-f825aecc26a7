'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Mail } from 'lucide-react'
import Image from 'next/image'
import InsightsIcon from '../../../../public/assets/icons/essentials-icon.svg'
import { useState } from 'react'
import { useJoinWaitingList } from '@/services/waiting-list.services'
import { z } from 'zod'

// Zod schema for email validation
const emailSchema = z.object({
    email: z
        .string()
        .min(1, 'Email is required')
        .email('Please enter a valid email address')
        .max(100, 'Email must be less than 100 characters'),
})

const JoinWaitingListPage = () => {
    return (
        <div className="relative -mt-17 md:-mt-19 lg:-mt-17 h-screen flex lg:rounded-[20px] lg:mx-7 mb-4 px-2 lg:px-0 justify-center items-center overflow-hidden">
            <Image
                src={'/assets/img/join-waiting-list-bg-grid.png'}
                fill
                priority
                alt="Join Waiting List Background"
                className="object-cover"
                quality={90}
            />
            <div className="bg-[#FFFFFF66] border z-10 p-6 rounded-[20px] backdrop-blur-2xl">
                <EmailCard />
            </div>
        </div>
    )
}

export default JoinWaitingListPage

const EmailCard = () => {
    const [email, setEmail] = useState('')
    const [validationError, setValidationError] = useState<string | null>(null)
    const [honeypotInput, setHoneypotInput] = useState('')

    const submitMutation = useJoinWaitingList({
        onSuccess: () => {
            setEmail('')
            setValidationError(null)
        },
    })

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        if (honeypotInput) return
        // Clear previous validation error
        setValidationError(null)

        // Validate email using Zod
        const validationResult = emailSchema.safeParse({ email })

        if (!validationResult.success) {
            // Extract the first validation error
            const firstError = validationResult.error.errors[0]
            setValidationError(firstError.message)
            return
        }

        // Submit if validation passes
        submitMutation.mutate(validationResult.data.email)
    }

    const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setEmail(e.target.value)
        // Clear validation error when user starts typing
        if (validationError) {
            setValidationError(null)
        }
    }

    return (
        <div className="w-full md:max-w-sm lg:max-w-2xl mx-auto text-center space-y-6 py-6">
            {/* Waitinglist Badge */}
            <div className="inline-flex items-center gap-1 bg-[#00000005] backdrop-blur-sm border border-gray-200 rounded-[6px] px-4 py-2 text-xs text-[#000000B2]">
                <Image src={InsightsIcon} width={14} height={14} alt="Insights Icon" />
                <p>Waitlist</p>
            </div>

            {/* Main Heading */}
            <h1 className="text-[20px] lg:text-[45px] font-serif font-bold text-[#18181B] leading-tight">
                Redefine How You Work.
            </h1>

            {/* Subtitle */}
            <p className="text-xs lg:text-[14px] px-6 text-[#71717A] lg:max-w-lg mx-auto leading-relaxed">
                Empower seamless workflows with Raydian, the AI task management platform for unmatched clarity and effortless team
                connection.
            </p>

            {/* Email Signup Form */}
            <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
                <div className=" flex-1">
                    <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                        <Input
                            type="email"
                            placeholder="Your mail address"
                            className={`pl-10 h-12 bg-white border-gray-200 text-gray-900 placeholder:text-gray-500 ${
                                validationError ? 'border-red-500 focus:border-red-500' : ''
                            }`}
                            name="email"
                            value={email}
                            onChange={handleEmailChange}
                        />
                        <input
                            type="text"
                            name="honeypot"
                            value={honeypotInput}
                            onChange={(e) => setHoneypotInput(e.target.value)}
                            className="hidden"
                        />
                    </div>
                    {validationError && <p className="text-red-500 text-xs ml-2 mt-1 text-left">{validationError}</p>}
                </div>
                <Button
                    type="submit"
                    className="h-12 px-6 bg-emerald-600 hover:bg-[#08B38B] text-white font-medium"
                    disabled={submitMutation.isPending}>
                    {submitMutation.isPending ? 'Joining...' : 'Join Waitinglist'}
                </Button>
            </form>
        </div>
    )
}
