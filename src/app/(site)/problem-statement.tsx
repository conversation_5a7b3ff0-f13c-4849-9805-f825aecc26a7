'use client'
import ReusableCarousel from '@/components/reusable-carousel'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import Image from 'next/image'
import { motion } from 'framer-motion'

const staticCardData = [
    {
        id: 1,
        title: 'Endless Meetings',
        description: 'Hours spent debating tasks and timelines instead of actually doing the work.',
        img: '/assets/img/endless-meetings.webp',
    },
    {
        id: 2,
        title: 'Lack of Clarity',
        description: 'Vague goals lead to confusing workflows, missed deadlines, and frustrated teams.',
        img: '/assets/img/lack-of-clarity.webp',
    },
    {
        id: 3,
        title: 'Manual Drudgery',
        description: 'Manually creating tasks, sub-tasks, and dependencies for every new project is slow and demotivating.',
        img: '/assets/img/manual-drudgery.webp',
    },
]

const ProblemStatement = () => {
    return (
        <section
            className="w-full max-w-6xl relative mx-auto rounded-[32px] my-4 sm:my-6 lg:my-2 pb-6 sm:pb-8 lg:pb-12 overflow-hidden"
            aria-labelledby="problem-heading"
            id="problem-statement">
            {/* Background Image */}
            <Image
                src="/assets/img/project-planning-bg-grid.webp"
                fill
                alt="Project planning and task management interface"
                className="absolute inset-0 object-cover rounded-[32px] hidden lg:block opacity-70"
                quality={80}
                sizes="(max-width: 1280px) 100vw, 1280px"
                style={{ zIndex: 0 }}
            />

            {/* Content Overlay */}
            <div className="relative z-10 px-4 sm:px-6 lg:px-8">
                {/* Problem Badge */}
                <div className="flex justify-center items-center mt-6 sm:mt-8">
                    <Badge className="bg-[#0000000D] text-[#000000] border border-[#E4E7EC] py-2 px-4 rounded-[8px] text-sm font-medium">
                        PROBLEM
                    </Badge>
                </div>

                {/* Main Heading */}
                <h2
                    id="problem-heading"
                    className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl 3xl:text-[64px] mx-auto font-serif font-medium text-center max-w-4xl text-[#18181B] leading-tight mt-6 sm:mt-8">
                    Project planning is chaotic. Your ambition deserves clarity.
                </h2>

                {/* Description */}
                <p className="text-sm sm:text-base md:text-lg lg:text-xl text-center text-[#000000] max-w-2xl mx-auto leading-relaxed mt-4 sm:mt-6">
                    Your team spends too much time on the process of planning and not enough on the progress that matters. It
                    leads to...
                </p>

                {/* Problem Cards */}
                <div className="hidden lg:grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6 mt-8 sm:mt-10 lg:mt-12">
                    {staticCardData.map((data) => (
                        <CardItem key={data.id} data={data} />
                    ))}
                </div>
                <div className="lg:hidden px-4 ml-4">
                    <ReusableCarousel
                        items={staticCardData.map((data) => ({ id: data.id, component: <CardItem data={data} /> }))}
                    />
                </div>

                {/* How It Works Section */}
                <div className="mt-12 sm:mt-16 lg:mt-20">
                    <div className="flex justify-center items-center">
                        <Badge className="bg-[#0000000D] text-[#000000] border border-[#E4E7EC] py-2 px-4 rounded-[8px] text-xs md:text-sm font-medium">
                            HOW IT WORKS
                        </Badge>
                    </div>
                    <h3 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl 3xl:text-[64px] mx-auto font-serif font-medium text-center max-w-4xl text-[#18181B] leading-tight mt-6 sm:mt-8">
                        Structure Your Entire Project in 3 Simple Steps.
                    </h3>
                </div>
            </div>
        </section>
    )
}

export default ProblemStatement

const CardItem = ({ data }: { data: (typeof staticCardData)[0] }) => {
    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-50px' }}
            transition={{ duration: 0.6, ease: 'easeOut' }}
            whileHover={{
                y: -8,
                scale: 1.02,
                transition: { duration: 0.3, ease: 'easeOut' },
            }}
            className="group cursor-pointer">
            <Card className="flex flex-col justify-between bg-white/95 border overflow-hidden rounded-[24px] shadow-none max-w-[307px] lg:max-w-full mx-auto transition-shadow duration-300 group-hover:shadow-lg group-hover:shadow-black/10">
                {/* Content Section */}
                <div className="p-4 sm:p-5 lg:p-4 flex-1">
                    <h3 className="text-lg sm:text-xl lg:text-[22px] font-medium font-serif pb-2 sm:pb-3 text-[#18181B] transition-colors group-hover:text-[#000000]">
                        {data.title}
                    </h3>
                    <p className="text-sm sm:text-base text-[#000000] font-medium leading-relaxed">{data.description}</p>
                </div>

                {/* Image Section */}
                <div className="relative h-[200px] sm:h-[220px] lg:h-[250px] overflow-hidden">
                    <Image
                        src={data.img}
                        alt={`Illustration showing ${data.title.toLowerCase()} problem in project management`}
                        fill
                        className="object-contain p-2 transition-transform duration-300 group-hover:scale-105"
                        quality={85}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                        loading="lazy"
                    />
                </div>
            </Card>
        </motion.div>
    )
}
