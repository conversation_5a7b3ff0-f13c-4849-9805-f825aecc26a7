'use client'

import { useTabStore } from '@/store/tabs.store'
import { useRouter } from 'next/navigation'
type CtaPropsType = {
    children: React.ReactNode
}

const NavigateToPricing = ({ children }: CtaPropsType) => {
    const router = useRouter()
    const { setSettingsActiveTab } = useTabStore()

    const handleClick = () => {
        setSettingsActiveTab('subscription')
        router.push('/app/settings')
    }
    return <div onClick={handleClick}>{children}</div>
}

export default NavigateToPricing
