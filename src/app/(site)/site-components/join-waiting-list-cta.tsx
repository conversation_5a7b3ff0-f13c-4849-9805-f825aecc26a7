'use client'

import { Button } from '../../../components/ui/button'
import { cn } from '@/lib/utils'
import Image from 'next/image'
import { usePathname, useRouter } from 'next/navigation'
const defaultClassName = 'flex w-full sm:w-fit h-[34px] px-3 bg-emerald-600 items-center gap-1 rounded-lg hover:bg-[#08B38B]'
type CtaPropsType = {
    showIcon?: boolean
    buttonClassName?: string
    iconText?: string
    showActive?: boolean
}

const JoinWaitingListCTA = ({ showIcon = true, buttonClassName, iconText = 'Sign In', showActive = false }: CtaPropsType) => {
    const router = useRouter()
    const path = usePathname()
    const handleClick = () => {
        if (iconText === 'Sign In') {
            router.push('/sign-in')
            return
        }
        if (iconText === 'Get Started For Free') {
            router.push('/sign-up')
            return
        }
        router.push('/join-waiting-list')
    }
    return (
        <Button
            variant="outline"
            onClick={handleClick}
            className={cn(
                defaultClassName,
                buttonClassName,
                showActive && path === '/join-waiting-list' ? 'border' : 'border-none',
            )}>
            <p className="text-white text-xs 3xl:text-sm font-medium 3xl:font-semibold mr-1">{iconText}</p>
            {showIcon && <Image src={'/assets/img/round-arrow-up-right.png'} width={20} height={20} alt="Arrow Right" />}
        </Button>
    )
}

export default JoinWaitingListCTA
