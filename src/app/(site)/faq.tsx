import { Accordion, Accordion<PERSON>ontent, Accordion<PERSON><PERSON>, AccordionTrigger } from '@/components/ui/accordion'
import { Badge } from '@/components/ui/badge'
import { Plus, Minus } from 'lucide-react'

export default function Faq() {
    const faqs = [
        {
            question: 'What is an AI Chatbot?',
            answer: 'An AI Chatbot is a computer program that uses artificial intelligence to simulate human conversation. It can understand and respond to text or voice inputs in a natural, conversational way, helping users with various tasks, answering questions, and providing assistance.',
        },
        {
            question: 'How does AI Chat work?',
            answer: 'AI Chat works by using natural language processing (NLP) and machine learning algorithms to understand user input, process the information, and generate relevant responses. The AI analyzes patterns in language and draws from its training data to provide helpful and contextual answers.',
        },
        {
            question: 'What can I ask an AI Chatbot?',
            answer: "You can ask an AI Chatbot a wide variety of questions including general knowledge queries, help with tasks like writing or coding, explanations of complex topics, creative assistance, problem-solving, and much more. The scope depends on the specific AI's capabilities and training.",
        },
        {
            question: 'Is my data safe when chatting with an AI?',
            answer: 'Data safety depends on the specific AI service provider. Reputable AI services implement security measures like encryption and privacy policies to protect user data. Always review the privacy policy and terms of service to understand how your conversations are handled and stored.',
        },
        {
            question: 'Can AI Chatbots learn from conversations?',
            answer: 'This varies by AI system. Some AI chatbots can learn and adapt from conversations to improve their responses, while others operate on fixed training data. Many modern AI systems use conversation data to enhance their performance, but this is typically done while maintaining user privacy.',
        },
        {
            question: 'What are the limitations of AI Chatbots?',
            answer: 'AI Chatbots have several limitations including potential inaccuracies in responses, inability to understand context in complex situations, lack of real-world experience, potential biases from training data, and inability to perform actions outside of text-based communication. They should be used as helpful tools rather than authoritative sources.',
        },
    ]

    return (
        <section className="md:max-w-md lg:max-w-4xl 3xl:max-w-5xl mx-auto px-8 md:px-4 lg:px-4 pt-8 lg:pt-0 pb-16 mt-0 lg:-mt-30 relative z-1">
            <div className="flex justify-center items-center mt-6 sm:mt-8 relative z-1 mb-4">
                <Badge className="bg-[#0000000D] text-[#000000] border border-[#E4E7EC] py-2 px-4 rounded-[8px]  text-xs md:text-sm font-medium">
                    FAQ
                </Badge>
            </div>
            <p className="text-[22px] md:text-3xl lg:text-[50px] font-semibold lg:font-medium font-serif text-center mb-6 lg:mb-12 text-gray-900">
                Frequently asked questions
            </p>

            <Accordion type="single" collapsible className="space-y-2 3xl:space-y-4">
                {faqs.map((faq, index) => (
                    <AccordionItem key={index} value={`item-${index}`} className="border-none shadow-none">
                        <AccordionTrigger className="text-left hover:no-underline py-3 px-6 [&>svg]:hidden group">
                            <span className="text-gray-700 font-medium pr-4">
                                {index + 1}. {faq.question}
                            </span>
                            <div className="shrink-0 ml-auto">
                                <Plus className="h-5 w-5 text-gray-500 group-data-[state=open]:hidden transition-transform duration-200" />
                                <Minus className="h-5 w-5 text-gray-500 group-data-[state=closed]:hidden transition-transform duration-200" />
                            </div>
                        </AccordionTrigger>
                        <AccordionContent className="px-6 pb-6 text-gray-600 leading-relaxed">{faq.answer}</AccordionContent>
                    </AccordionItem>
                ))}
            </Accordion>
        </section>
    )
}
