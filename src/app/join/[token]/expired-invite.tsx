'use client'

import { useRouter } from 'next/navigation'

import Image from 'next/image'
import { Button } from '@/components/ui/button'

export default function ExpiredInvite() {
    const router = useRouter()

    const handleNavigateToApp = async () => {
        router.push('/app')
    }

    return (
        <div className="max-w-md shadow-lg overflow-hidden rounded-[12px]">
            <Image
                src="/assets/img/expired-invite.png"
                width={460}
                height={230}
                alt="Logo"
                className="w-full h-auto object-cover"
            />
            <div className="p-6 w-full mx-auto text-center space-y-4 ">
                <p className="px-4 mx-auto text-[#18181B] text-lg font-semibold">
                    We&apos;re sorry, this invitation link is no longer active. It might have expired.
                </p>
                <Button className="text-[15px] font-medium" onClick={handleNavigateToApp}>
                    Go To My Workspace
                </Button>
            </div>
        </div>
    )
}
