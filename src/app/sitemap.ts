import { MetadataRoute } from 'next'
import fs from 'fs'
import path from 'path'

export default function sitemap(): MetadataRoute.Sitemap {
    const baseUrl = 'https://raydian.ai'
    const currentDate = new Date().toISOString()

    // Helper: recursively collect doc paths
    const getDocsUrls = (dir: string, basePath = '/docs'): string[] => {
        const entries = fs.readdirSync(dir, { withFileTypes: true })
        const urls: string[] = []

        for (const entry of entries) {
            const fullPath = path.join(dir, entry.name)
            const routePath = `${basePath}/${entry.name}`.replace(/\/page\.tsx$|\/index\.tsx$/, '').replace(/\/$/, '')

            if (entry.isDirectory()) {
                urls.push(routePath)
                urls.push(...getDocsUrls(fullPath, routePath))
            } else if (entry.isFile() && (entry.name === 'page.tsx' || entry.name === 'index.tsx')) {
                urls.push(basePath)
            }
        }

        // Deduplicate
        return Array.from(new Set(urls))
    }

    const docsDir = path.join(process.cwd(), 'src/app/docs')
    const docsUrls = getDocsUrls(docsDir)

    return [
        // === Main site pages ===
        {
            url: `${baseUrl}`,
            lastModified: currentDate,
            changeFrequency: 'weekly',
            priority: 1.0,
        },
        {
            url: `${baseUrl}/pricing`,
            lastModified: currentDate,
            changeFrequency: 'monthly',
            priority: 0.8,
        },

        // === Auto-generated Docs pages ===
        ...docsUrls.map((url) => ({
            url: `${baseUrl}${url}`,
            lastModified: currentDate,
            changeFrequency: 'weekly' as const,
            priority: url === '/docs' ? 1.0 : 0.8,
        })),
    ]
}
