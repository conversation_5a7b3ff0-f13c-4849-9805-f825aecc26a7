// app/robots.ts
import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
    return {
        rules: [
            {
                userAgent: '*',
                allow: ['/', '/docs/', '/docs/getting-started/', '/docs/guides/', '/docs/api/'],
                disallow: ['/app/', '/api/', '/auth/', '/docs/search/', '/docs/internal/'],
            },
            {
                userAgent: 'Googlebot',
                allow: ['/', '/docs/', '/docs/getting-started/', '/docs/guides/', '/docs/api/'],
                disallow: ['/app/', '/api/', '/auth/', '/docs/search/', '/docs/internal/'],
            },
        ],
        sitemap: 'https://raydian.ai/sitemap.xml',
    }
}
