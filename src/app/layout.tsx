// app/layout.tsx
import type { Metadata } from 'next'
import './globals.css'
import { Toaster } from '@/components/ui/sonner'
import QueryProvider from '@/components/query-provider'
import { <PERSON>, Besley } from 'next/font/google'
import Analytics from '@/components/analytics-tracker'

export const metadata: Metadata = {
    title: {
        default: 'Raydian.ai - AI Project Management Software for Startups 2025',
        template: '%s | Raydian.ai',
    },
    description:
        'Raydian.ai is an AI-powered project management software for startups and software teams in 2025. Transform your ideas into structured workflows with intelligent planning, automation, and real-time collaboration.',
    keywords: [
        'AI project management software 2025',
        'AI planning tool for startups',
        'project workflow automation',
        'startup project management software',
        'AI productivity software for teams',
        'AI co-pilot for project planning',
        'software project planner 2025',
        'AI assistant for startups',
        'AI-powered project management tool',
        'Raydian.ai project planning software',
    ],
    authors: [{ name: 'Raydian.ai' }],
    creator: 'Raydian.ai',
    metadataBase: new URL('https://raydian.ai'),
    alternates: {
        canonical: 'https://raydian.ai',
    },
    icons: {
        icon: '/favicon_io/favicon.ico',
    },
    openGraph: {
        title: 'Raydian.ai - AI Project Management Software for Startups 2025',
        description:
            'AI-driven project management tool for startups and teams. Plan, organize, and execute your projects intelligently with Raydian.ai — built for 2025 and beyond.',
        url: 'https://raydian.ai',
        siteName: 'Raydian.ai',
        images: [
            {
                url: 'https://minio.raydian.ai/raydian-static/opengraph-image.png',
                width: 1200,
                height: 630,
                alt: 'Raydian.ai AI project planning dashboard',
            },
        ],
        locale: 'en_US',
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Raydian.ai - AI Project Management Software for Startups 2025',
        description:
            'Plan, automate, and collaborate better with Raydian.ai — the AI project management tool designed for startups and software teams in 2025.',
        images: ['https://minio.raydian.ai/raydian-static/opengraph-image.png'],
    },
    robots: {
        index: true,
        follow: true,
        googleBot: {
            index: true,
            follow: true,
            'max-video-preview': -1,
            'max-image-preview': 'large',
            'max-snippet': -1,
        },
    },
    category: 'technology',
    classification: 'AI Software for Startups',
    referrer: 'origin-when-cross-origin',
}

// Load fonts from Google with optimization
const inter = Inter({
    subsets: ['latin'],
    display: 'swap',
    variable: '--font-inter',
})

const besley = Besley({
    subsets: ['latin'],
    display: 'swap',
    variable: '--font-besley',
})

const googleAnalyticsId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID

export default function RootLayout({ children }: { children: React.ReactNode }) {
    return (
        <html lang="en" className={`${inter.variable} ${besley.variable}`}>
            <head>
                <script
                    type="application/ld+json"
                    dangerouslySetInnerHTML={{
                        __html: JSON.stringify({
                            '@context': 'https://schema.org',
                            '@type': 'SoftwareApplication',
                            name: 'Raydian.ai',
                            url: 'https://raydian.ai',
                            applicationCategory: 'ProjectManagementApplication',
                            operatingSystem: 'Web',
                            description:
                                'Raydian.ai is an AI-powered project management and planning tool that helps teams and startups turn ideas into structured workflows and actionable plans.',
                            offers: {
                                '@type': 'Offer',
                                price: '0',
                                priceCurrency: 'USD',
                            },
                            creator: {
                                '@type': 'Organization',
                                name: 'Raydian.ai',
                                url: 'https://raydian.ai',
                            },
                            sameAs: ['https://x.com/RaydianAi', 'https://www.linkedin.com/company/raydian-ai'],
                        }),
                    }}
                />
            </head>
            <body className="antialiased font-sans">
                {googleAnalyticsId && <Analytics measurementId={googleAnalyticsId} />}
                <QueryProvider>{children}</QueryProvider>
                <Toaster />
            </body>
        </html>
    )
}
