'use client'

import React, { useEffect, useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Form, FormField } from '@/components/ui/form'

import { Loader } from 'lucide-react'
import { useAuthStore } from '@/store/auth.store'
import { useRouter, useSearchParams } from 'next/navigation'
import { toast } from 'sonner'
import FormOverlayWrapper from '@/components/form-overlay-wrapper'
import { FormInputField, PasswordInputField } from '@/components/form-fields'

const formSchema = z.object({
    email: z.string().min(1, 'Required').email('Invalid email'),
    password: z.string().min(1, 'Password required'),
})

const SignInPage = () => {
    const [isSubmitting, setIsSubmitting] = useState(false)
    const login = useAuthStore((state) => state.login)
    const router = useRouter()
    const searchParams = useSearchParams()
    const redirectUrl = searchParams.get('redirect')
    const { setInvitationUrl } = useAuthStore()

    useEffect(() => {
        setInvitationUrl(redirectUrl)
    }, [redirectUrl, setInvitationUrl])

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            email: '',
            password: '',
        },
    })

    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        const toastId = toast.loading('Logging in...')
        setIsSubmitting(true)
        try {
            const result = await login(values)
            if (result.success) {
                router.push(redirectUrl || '/app/chat')
                toast.success('Login successful!', { id: toastId })
            } else {
                toast.error(result.message || 'An error occurred during login.', { id: toastId })
            }
        } catch (error) {
            console.error('Login failed:', error)
            toast.error('An unexpected error occurred. Please try again.', { id: toastId })
        } finally {
            setIsSubmitting(false)
        }
    }

    const handleGoogleLogin = () => {
        window.location.href = `${process.env.NEXT_PUBLIC_API_URL}/auth/google`
    }
    const handleMicrosoftLogin = () => {
        window.location.href = `${process.env.NEXT_PUBLIC_API_URL}/auth/microsoft`
    }

    return (
        <FormOverlayWrapper isSubmitting={isSubmitting}>
            <Card className="max-w-xl border shadow-none mt-[0px] w-[380px] bg-[#FDFDFD] rounded-3xl">
                <CardHeader>
                    <CardTitle className="text-2xl py-2">Welcome back.</CardTitle>
                    <CardDescription>Enter your email below to login to your account.</CardDescription>
                </CardHeader>

                <CardContent>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                            {/* Email Field */}
                            <FormField
                                control={form.control}
                                name="email"
                                render={({ field }) => (
                                    <FormInputField
                                        label="Email *"
                                        placeholder="Your email"
                                        type="email"
                                        field={field}
                                        inputClassName="bg-white rounded-sm"
                                    />
                                )}
                            />

                            {/* Password Field */}

                            <FormField
                                control={form.control}
                                name="password"
                                render={({ field }) => (
                                    <PasswordInputField
                                        label="Password *"
                                        placeholder="Your password"
                                        field={field}
                                        inputClassName="rounded-sm"
                                    />
                                )}
                            />
                            <div className="flex justify-end">
                                <Link href="forgot-password" className="underline text-[13px]">
                                    Forgot your password?
                                </Link>
                            </div>

                            {/* Submit Button */}
                            <Button type="submit" className="w-full rounded-sm mt-1" disabled={isSubmitting}>
                                {isSubmitting ? <Loader className="animate-spin" /> : 'Login'}
                            </Button>
                        </form>
                    </Form>

                    {/* OR separator and social logins */}
                    <div className="text-center space-y-4 mt-6">
                        <div className="my-4 flex items-center">
                            <Separator style={{ width: '33%', backgroundColor: '#E9EAEB' }} />
                            <span className="mx-2 text-xs text-gray-500">or continue with</span>
                            <Separator style={{ width: '34%', backgroundColor: '#E9EAEB' }} />
                        </div>
                        <div className="flex w-full justify-between">
                            <Button variant="outline" className="w-[48%] bg-[#fff] rounded-sm" onClick={handleGoogleLogin}>
                                <div className="flex items-center justify-center w-full">
                                    <Image
                                        src={'/assets/img/GoogleLogo.svg'}
                                        alt="Google Logo"
                                        width={20}
                                        height={20}
                                        className="mr-2"
                                    />
                                </div>
                            </Button>

                            <Button variant="outline" className="w-[48%] bg-[#fff] rounded-sm" onClick={handleMicrosoftLogin}>
                                <div className="flex items-center justify-center w-full pl-2">
                                    <Image
                                        src={'/assets/img/MsLogo.svg'}
                                        alt="Microsoft Logo"
                                        width={20}
                                        height={20}
                                        className="mr-2"
                                    />
                                </div>
                            </Button>
                        </div>

                        <div className="text-sm">
                            Don&apos;t have an account?{' '}
                            <Link href={redirectUrl ? `/sign-up?redirect=${redirectUrl}` : '/sign-up'} className="underline">
                                Sign Up
                            </Link>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </FormOverlayWrapper>
    )
}

export default SignInPage
