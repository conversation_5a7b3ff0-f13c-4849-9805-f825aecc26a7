import Image from 'next/image'

export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <div className="flex flex-row w-screen h-screen relative">
            <Image
                src="/assets/img/auth-background-image.png"
                fill
                alt="Auth background image"
                className="absolute object-cover h-full w-full -z-10"
            />
            <div className="w-full flex justify-center items-center overflow-auto custom-scroll ">
                {/* <Link
                    href="/"
                    className=" flex items-center gap-2 hover:opacity-80 transition-opacity absolute top-20 left-0 md:top-12 sm:top-0 md:left-52 sm:left-20  z-10">
                    <Image src="/assets/img/raydian-logo.png" alt="Logo" width={28} height={28} className="h-auto w-auto" />
                    <span className="font-semibold text-lg">Raydian</span>
                </Link> */}

                <div className="z-1">{children}</div>
            </div>
        </div>
    )
}
