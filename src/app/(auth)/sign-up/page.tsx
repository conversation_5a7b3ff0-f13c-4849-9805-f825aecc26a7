'use client'

import dynamic from 'next/dynamic'
import { Suspense } from 'react'
import { Loader } from 'lucide-react'

const SignUpContent = dynamic(() => import('./sign-up-form'), {
    ssr: false,
})

export default function Page() {
    return (
        <Suspense
            fallback={
                <div className="flex justify-center items-center min-h-screen">
                    <Loader className="w-6 h-6 animate-spin" />
                </div>
            }>
            <SignUpContent />
        </Suspense>
    )
}
