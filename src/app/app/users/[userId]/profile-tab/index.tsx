'use client'

import React, { useMemo } from 'react'
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { useTabStore } from '@/store/tabs.store'
import OverviewTabContent from './overview'
import { User } from '@/types/user'
import AchievementsTabContent from './achievements'

interface TabItem {
    id: string
    label: string
    content: React.ReactNode
}

export default function ProfileTabs({ user }: { user: User }) {
    const { teamManagementActiveTab, setTeamManagementActiveTab } = useTabStore()

    // Create an array of tab items with optional count badges
    const tabItems: TabItem[] = useMemo(
        () => [
            { id: 'overview', label: 'Overview', content: <OverviewTabContent user={user} /> },
            { id: 'achievements', label: 'Achievements', content: <AchievementsTabContent user={user} /> },
            { id: 'testimoniels', label: 'Testimonials', content: <></> },
        ],
        [user],
    )

    return (
        <div className="flex rounded-lg bg-transparent pb-4 items-center">
            <Tabs value={teamManagementActiveTab} onValueChange={setTeamManagementActiveTab} className="w-full">
                <div className="flex flex-row justify-between items-center w-full pb-0 h-[26px] border-b-2 mb-2">
                    <TabsList className="flex gap-2 h-full bg-transparent p-0">
                        {tabItems.map((tab) => (
                            <TabsTrigger
                                key={tab.id}
                                value={tab.id}
                                className="rounded-[3px] h-[28px] px-2 pb-1 data-[state=active]:border-b-3 data-[state=active]:border-b-[#2FA87D] data-[state=active]:shadow-none data-[state=active]:text-[#2FA87D]  data-[state=inactive]:text-[#575757] cursor-pointer">
                                <span>{tab.label}</span>
                            </TabsTrigger>
                        ))}
                    </TabsList>
                </div>
                {tabItems.map((tab) => (
                    <TabsContent key={tab.id} value={tab.id}>
                        {tab.content}
                    </TabsContent>
                ))}
            </Tabs>
        </div>
    )
}
