'use client'

import { useEffect, useState, useCallback, useMemo } from 'react'

import { useBreadcrumbStore } from '@/store/breadcrumb.store'
import Kanban from './kanban-view'
import ListView from './list-view'

import { Label } from '@/components/ui/label'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { format, parseISO } from 'date-fns'
import { DnDTaskType as CardType } from '@/components/kanban-dnd/types'
import { DebouncedSearch } from '@/components/debounced-search'
import { Building, CircleCheck, FolderOpen, LayoutGrid, LayoutList, Puzzle, Share2, Users, Bug } from 'lucide-react'
import { FilterDialog } from '@/components/filter/filter-dialog'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { CreateTaskModal } from './create-task'
import { useTabStore } from '@/store/tabs.store'
import { useAuthStore } from '@/store/auth.store'
import { useSearchParams } from 'next/navigation'
import { useFilterStore } from '@/store/filter-store'
import { useKanbanStore } from '@/store/kanban-dnd-store'
import Bookmarked from '@icons/bookmarked.svg'
import Bookmark from '@icons/bookmark.svg'
import Image from 'next/image'

interface APITask extends Omit<CardType, 'column'> {
    taskStatus: {
        id: number
        label: string
        short_code: string
        colour: string
    }
    assignedUser: {
        id: number
        first_name: string
        last_name: string
    }
    priority?: string
}

// Extend CardType to include targetColumn

const updateTask = async (task: CardType): Promise<CardType> => {
    const response = await api.put(endpoints.tasks.updateTask + '/' + task.id, { ...task })
    return response.data
}

export default function Page() {
    const { setBreadcrumbs } = useBreadcrumbStore()
    const [searchValue, setSearchValue] = useState('')
    const [showBookmarkedTasks, setShowBookmarkedTasks] = useState(false)
    const queryClient = useQueryClient()
    const [activeFilters, setActiveFilters] = useState<Record<string, number[]>>({})
    const { viewMode, setViewMode } = useTabStore()
    const { setSelectedFilters, setTempSelectedFilters } = useFilterStore()
    const { setTasks } = useKanbanStore()
    const featureId = Number(useSearchParams().get('featureId'))

    // Use state to store workspace ID to avoid hydration issues
    const [workspaceId, setWorkspaceId] = useState<number | null>(null)
    const { currentWorkspace } = useAuthStore()

    // Only access currentWorkspace on the client side
    useEffect(() => {
        if (currentWorkspace?.id) {
            setWorkspaceId(currentWorkspace.id)
        }
    }, [currentWorkspace])

    useEffect(() => {
        if (featureId) {
            setSelectedFilters({ feat_id: [featureId] })
            setTempSelectedFilters({ feat_id: [featureId] })
            handleFiltersChange({ feat_id: [featureId] })
        }
        return () => {
            setTempSelectedFilters({})
            handleFiltersChange({})
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [featureId])

    // Memoize the fetch function to prevent it from changing on every render
    const fetchTasks = useCallback(async (): Promise<CardType[]> => {
        // Don't fetch if workspaceId is not available yet
        if (!workspaceId) {
            return []
        }

        // Prepare the params object with all selected filters
        const queryParams: Record<string, unknown> = {}

        // Process activeFilters to format them for the API
        Object.entries(activeFilters).forEach(([category, values]) => {
            if (values.length > 0) {
                // For multiple values in a category, use array format
                queryParams[category] = values
            }
        })

        // Add search value if it exists
        if (searchValue) {
            queryParams.searchQuery = searchValue
        }

        // Add workspace ID
        queryParams.workspace_id = workspaceId
        queryParams.showOnlyBookmarks = showBookmarkedTasks

        const response = await api.get(endpoints.tasks.getTasks, {
            params: queryParams,
        })

        const taskDetails = response.data.data.map((task: APITask) => ({
            ...task,
            id: task.id,
            status_id: task.taskStatus.id,
            column: task.taskStatus.label,
            title: task.title,
            description: task.description,
            priority: task.priority || 'low',
            dueDate: task.due_date ? format(parseISO(task.due_date as string), 'dd/MM/yy') : undefined,
            taskStatus: task.taskStatus,
        }))
        setTasks(taskDetails)
        return taskDetails
    }, [activeFilters, searchValue, workspaceId, setTasks, showBookmarkedTasks])

    // Fetch tasks query with proper dependencies
    const {
        data: tasks = [],
        isLoading,
        refetch: refetchTasks,
    } = useQuery({
        queryKey: ['tasks', activeFilters, searchValue, workspaceId, showBookmarkedTasks],
        queryFn: fetchTasks,
        enabled: !!workspaceId, // Only run the query when workspaceId is available
    })
    // Update task mutation
    const updateTaskMutation = useMutation({
        mutationFn: updateTask,
        onSuccess: () => {
            // Invalidate and refetch tasks after successful update
            if (viewMode === 'list') queryClient.invalidateQueries({ queryKey: ['tasks'] })
        },
    })

    useEffect(() => {
        setBreadcrumbs([
            { label: 'Dashboard', href: '/app' },
            { label: 'Planner', href: '/app/planner' },
        ])
    }, [setBreadcrumbs])

    const handleTaskUpdate = async (task: CardType) => {
        try {
            const body = {
                status: task.status_id,
                priority: task.priority,
                ...task,
            }

            await updateTaskMutation.mutateAsync(body)
        } catch (error) {
            throw new Error(`Failed to update task: ${error}`)
        }
    }

    const handleSearchEnd = (value: string) => {
        setSearchValue(value)
    }

    // Memoize filter categories to prevent recreation on each render
    const filterCategories = useMemo(
        () => [
            {
                id: 'projects',
                label: 'Projects',
                icon: <FolderOpen className="h-4 w-4 text-[#9BA5B1]" />,
                count: 6,
                endpoint: endpoints.meta.getProjects,
                isDefault: true,
            },
            {
                id: 'feat_id',
                label: 'Features',
                icon: <Puzzle className="h-4 w-4 text-[#9BA5B1]" />,
                count: 99,
                endpoint: endpoints.meta.getFeatures,
            },
            {
                id: 'status',
                label: 'Status',
                icon: <CircleCheck className="h-4 w-4 text-[#9BA5B1]" />,
                count: 4,
                endpoint: endpoints.meta.getStatuses,
            },
            {
                id: 'assignee',
                label: 'Assignee',
                icon: <Users className="h-4 w-4 text-[#9BA5B1]" />,
                count: 10,
                endpoint: endpoints.meta.getAssignees,
            },
            {
                id: 'department',
                label: 'Departments',
                icon: <Building className="h-4 w-4 text-[#9BA5B1]" />,
                count: 99,
                endpoint: endpoints.meta.getDepartments,
            },
            {
                id: 'bug',
                label: 'Bug',
                icon: <Bug className="h-4 w-4 text-[#9BA5B1]" />,
                count: 1,
                endpoint: endpoints.meta.getBugs,
            },
        ],
        []
    )

    // Handle filter changes
    const handleFiltersChange = (filters: Record<string, number[]>) => {
        setActiveFilters(filters)
    }

    const handleBookmarkClick = async () => {
        setShowBookmarkedTasks(!showBookmarkedTasks)
    }

    return (
        <div className="w-full space-y-4 max-h-[88dvh]">
            <Label className="text-xl">Raydian Planner</Label>
            <div className="flex justify-between w-full gap-4 h-[39px]">
                <DebouncedSearch onSearchEnd={handleSearchEnd} className="w-[300px] lg:w-[500px] xl:w-[70%]" />
                <div className="flex items-center space-x-3 h-full">
                    <Tabs
                        defaultValue="kanban"
                        value={viewMode}
                        onValueChange={(value) => setViewMode(value as 'kanban' | 'list')}
                        className="m-w-[200px] h-full">
                        <TabsList className="grid w-full grid-cols-2 h-full min-w-[170px]">
                            <TabsTrigger value="kanban" className="text-[#09090B] data-[state=inactive]:text-[#09090B80] ">
                                <div className="flex items-center gap-2 px-4">
                                    <LayoutGrid className="h-4 w-4" />
                                    <p className="text-sm font-medium hidden lg:block">Kanban</p>
                                </div>
                            </TabsTrigger>
                            <TabsTrigger value="list" className="text-[#09090B] data-[state=inactive]:text-[#09090B80] ">
                                <div className="flex items-center gap-2 px-4">
                                    <LayoutList className="h-4 w-4" />
                                    <p className="text-sm font-medium hidden lg:block">List</p>
                                </div>
                            </TabsTrigger>
                        </TabsList>
                    </Tabs>
                </div>
                <FilterDialog categories={filterCategories} onFiltersChange={handleFiltersChange} />
                <Button variant="outline" className="rounded-sm h-full" onClick={handleBookmarkClick}>
                    {showBookmarkedTasks ? (
                        <div className="cursor-pointer h-[19px] w-[12px] relative">
                            <Image src={Bookmarked} alt="Bookmark" fill className="object-cover" />
                        </div>
                    ) : (
                        <div className="cursor-pointer h-[19px] w-[12px] relative">
                            <Image src={Bookmark} alt="Bookmark" fill className="object-cover" />
                        </div>
                    )}
                </Button>
                <Button variant="outline" className="rounded-sm h-full">
                    <div className="flex items-center gap-2">
                        <Share2 className="h-4 w-4" />
                        <p className="hidden lg:block">Share</p>
                    </div>
                </Button>
                {/* <CreateTaskModal /> */}
                <CreateTaskModal />
            </div>
            {viewMode === 'kanban' ? (
                <Kanban refetchTasks={refetchTasks} />
            ) : (
                <ListView
                    isUpdating={updateTaskMutation.isPending}
                    onTaskUpdate={handleTaskUpdate}
                    items={tasks}
                    isLoading={isLoading}
                    refetchTasks={refetchTasks}
                />
            )}
        </div>
    )
}
