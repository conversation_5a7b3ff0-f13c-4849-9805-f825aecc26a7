'use client'

import { useMutation, useQuery } from '@tanstack/react-query'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import { Loader } from 'lucide-react'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import KanbanDnDBoard from '@/components/kanban-dnd'
import { useKanbanStore } from '@/store/kanban-dnd-store'
import { DnDColumnType } from '@/components/kanban-dnd/types'
import { useTabStore } from '@/store/tabs.store'
import { AxiosError } from 'axios'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'

interface KanbanViewProps {
    refetchTasks: () => void
}

type UpdateApiBody = {
    id?: number
    feat_id?: number
    short_code?: string
    title?: string
    description?: string
    time_estimate_hrs?: number
    start_date?: string
    due_date?: string
    status?: number
    assignee?: number
    department?: number | null
}

interface KanbanColumnsResponse {
    data: DnDColumnType[]
}

const updateTask = async (task: UpdateApiBody): Promise<UpdateApiBody> => {
    const response = await api.put(endpoints.tasks.updateTask + '/' + task.id, { ...task })
    return response.data
}
export default function Kanban({ refetchTasks }: KanbanViewProps) {
    const { setColumns, setTaskUpdateApiBody, columns, taskUpdateApiBody } = useKanbanStore()
    const { viewMode } = useTabStore()

    const getColumns = async () => {
        const response = await api.get<KanbanColumnsResponse>(endpoints.kanban.getColumns)
        setColumns(response.data.data)
        return response.data
    }

    const {
        data: columnsData,
        isLoading: isLoadingColumns,
        refetch,
    } = useQuery<KanbanColumnsResponse>({
        queryKey: ['kanban-columns'],
        queryFn: getColumns,
        enabled: viewMode === 'kanban',
    })

    const updateTaskMutation = useMutation({
        mutationFn: updateTask,
        onSuccess: () => {
            setTaskUpdateApiBody(null)
            refetchTasks()
        },
        onError: async (error: AxiosError) => {
            await refetch()
            if (columnsData?.data) {
                setColumns(columnsData.data)
                refetchTasks()
            }
            axiosErrorToast(error, 'Failed to update task')
            setTaskUpdateApiBody(null)
        },
    })

    const handleDragComplete = async () => {
        if (taskUpdateApiBody) {
            const body = {
                id: taskUpdateApiBody?.id,
                feat_id: taskUpdateApiBody?.feat_id,
                short_code: taskUpdateApiBody?.short_code,
                title: taskUpdateApiBody?.title,
                description: taskUpdateApiBody?.description,
                // time_estimate_hrs: taskUpdateApiBody?.time_estimate_hrs,
                start_date: taskUpdateApiBody?.start_date || new Date().toISOString(),
                due_date: taskUpdateApiBody?.due_date,
                status: taskUpdateApiBody?.status,
                assignee: taskUpdateApiBody?.assignedUser?.id,
                department: taskUpdateApiBody?.department,
                movedToIndex: taskUpdateApiBody.movedToIndex,
            }
            await updateTaskMutation.mutateAsync(body)
        }
    }

    if (isLoadingColumns) {
        return (
            <div className="flex items-center justify-center h-[88dvh]">
                <Loader className="animate-spin" />
            </div>
        )
    }

    return (
        <ScrollArea className=" overflow-hidden py-4">
            {columns.length > 0 ? (
                <div className="w-full">
                    <KanbanDnDBoard onDragComplete={handleDragComplete} />
                </div>
            ) : (
                <div className="flex justify-center w-full items-center m-auto text-center border border-dashed rounded-md h-100 text-xl font-bold text-zinc-700">
                    No details to show
                </div>
            )}
            <ScrollBar orientation="horizontal" />
        </ScrollArea>
    )
}
