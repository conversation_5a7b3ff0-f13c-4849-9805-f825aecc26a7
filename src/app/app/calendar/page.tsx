'use client'
import { useEffect } from 'react'

import { useBreadcrumbStore } from '@/store/breadcrumb.store'

export default function Page() {
    const { setBreadcrumbs } = useBreadcrumbStore()

    useEffect(() => {
        setBreadcrumbs([
            { label: 'Dashboard', href: '/' },
            { label: 'Calendar', href: '/app/calendar' },
        ])
    }, [setBreadcrumbs])

    return (
        <div>
            <h1>Calendar Page</h1>
            <p>This is the Calendar page.</p>
        </div>
    )
}
