'use client' // If using app directory

import { useState } from 'react'
import { MoreHorizontal } from 'lucide-react' // Optional icon
import { UpdateTaskModal } from './update-task'
import { DeleteWithAlert } from '@/components/delete-with-alert-dialog'
import { TaskFormValues } from '@/app/app/planner/create-task/create-task-form'
import endpoints from '@/services/api-endpoints'
import { TaskData } from './task-details-card'
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Dialog } from '@/components/ui/dialog'

interface TaskDropdownProps {
    updateDefaultValues: TaskFormValues & { taskId: number; shortCode: string }
    refetchTask: () => void
    taskData: TaskData
    onAftedDeleteSuccess: () => void
}

export default function TaskActionsDropdown({
    updateDefaultValues,
    refetchTask,
    taskData,
    onAftedDeleteSuccess,
}: TaskDropdownProps) {
    const [isMenuOpen, setIsMenuOpen] = useState(false)
    const [editOpen, setEditOpen] = useState(false)
    const [deleteOpen, setDeleteOpen] = useState(false)

    const handleEdit = () => {
        setIsMenuOpen(false) // Close dropdown first
        setTimeout(() => setEditOpen(true), 100) // Small delay to avoid conflicts
    }

    const handleDelete = () => {
        setIsMenuOpen(false) // Close dropdown first
        setTimeout(() => setDeleteOpen(true), 100) // Small delay to avoid conflicts
    }

    return (
        <>
            <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
                <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={handleEdit}>Edit</DropdownMenuItem>
                    <DropdownMenuItem onClick={handleDelete}>Delete</DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>

            <Dialog open={editOpen} onOpenChange={setEditOpen}>
                <UpdateTaskModal
                    defaultValues={updateDefaultValues}
                    onAfterSuccess={refetchTask}
                    isUpdateOpen={editOpen}
                    setIsUpdateOpen={setEditOpen}
                />
            </Dialog>

            <Dialog open={deleteOpen} onOpenChange={setDeleteOpen}>
                <DeleteWithAlert
                    title="Are you sure you want to delete this task?"
                    description="This action cannot be undone."
                    endpoint={`${endpoints.tasks.deleteTask}/${taskData.id}`}
                    onAfterSuccess={onAftedDeleteSuccess}
                    isAlertOpen={deleteOpen}
                    setIsAlertOpen={setDeleteOpen}
                />
            </Dialog>
        </>
    )
}
