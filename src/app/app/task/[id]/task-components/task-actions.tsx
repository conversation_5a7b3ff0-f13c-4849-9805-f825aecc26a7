'use client' // If using app directory

import { useState } from 'react'
import { <PERSON>ci<PERSON>, Share2, Trash2 } from 'lucide-react' // Optional icon
import { UpdateTaskModal } from './update-task'
import { DeleteWithAlert } from '@/components/delete-with-alert-dialog'
import { TaskFormValues } from '@/app/app/planner/create-task/create-task-form'
import endpoints from '@/services/api-endpoints'
import { TaskData } from './task-details-card'
import { Button } from '@/components/ui/button'
import { bookmarkTask } from '@/services/task-bookmark.service'
import Bookmarked from '@icons/bookmarked.svg'
import Bookmark from '@icons/bookmark.svg'
import Image from 'next/image'

interface TaskActionsProps {
    updateDefaultValues: TaskFormValues & { taskId: number; shortCode: string }
    refetchTask: () => void
    taskData: TaskData
    onAftedDeleteSuccess: () => void
}

export default function TaskActions({ updateDefaultValues, refetchTask, taskData, onAftedDeleteSuccess }: TaskActionsProps) {
    const [editOpen, setEditOpen] = useState(false)
    const [deleteOpen, setDeleteOpen] = useState(false)

    const handleEdit = () => {
        setEditOpen(true)
    }

    const handleDelete = () => {
        setDeleteOpen(true)
    }

    const handleBookmark = async () => {
        await bookmarkTask(taskData.id)
        refetchTask()
    }

    return (
        <>
            <div className="flex max-h-8">
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={handleBookmark}>
                    {taskData?.isBookmarked ? (
                        <div className="cursor-pointer h-[19px] w-[12px] relative">
                            <Image src={Bookmarked} alt="Bookmark" fill className="object-cover" />
                        </div>
                    ) : (
                        <div className="cursor-pointer h-[19px] w-[12px] relative">
                            <Image src={Bookmark} alt="Bookmark" fill className="object-cover" />
                        </div>
                    )}
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={handleEdit}>
                    <Pencil className="h-4 w-4" color="#5B6871" />
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => console.log('share')}>
                    <Share2 className="h-4 w-4" color="#5B6871" />
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={handleDelete}>
                    <Trash2 className="h-4 w-4" color="#5B6871" />
                </Button>
            </div>

            <UpdateTaskModal
                defaultValues={updateDefaultValues}
                onAfterSuccess={refetchTask}
                isUpdateOpen={editOpen}
                setIsUpdateOpen={setEditOpen}
            />

            <DeleteWithAlert
                title="Are you sure you want to delete this task?"
                description="This action cannot be undone."
                endpoint={`${endpoints.tasks.deleteTask}/${taskData.id}`}
                onAfterSuccess={onAftedDeleteSuccess}
                isAlertOpen={deleteOpen}
                setIsAlertOpen={setDeleteOpen}
            />
        </>
    )
}
