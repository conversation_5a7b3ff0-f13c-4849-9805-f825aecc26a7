import { Accordion, Accordion<PERSON>ontent, Accordion<PERSON><PERSON>, AccordionTrigger } from '@/components/ui/accordion'
import DependentTaskIcon from '../../../../../../public/assets/icons/dependent-task-icon.png'
import Image from 'next/image'
import DepartmentChip from '@/components/ui/department-chip'
import getStatusIcon from '@/components/get-status-icon'
import getStatusStyles from '@/utils/get-status-styles.utils'
import { cn } from '@/lib/utils'
import { Link2 } from 'lucide-react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'

type TaskDepartmentType = {
    id: number
    short_code: string
    label: string
}
type DependentTaskType = {
    id: number
    short_code: string
    title: string
    status_id: number
    taskStatus: {
        id: number
        short_code: string
        label: string
    }
    task_dependency_mapping: {
        dependency_level: number
    }
    taskDepartment?: TaskDepartmentType
    taskAssignees?:
        | [
              {
                  id: number
                  first_name: string
                  last_name: string
                  img_url?: string
              }
          ]
        | []
}

type DependentTasks = DependentTaskType[]

export default function DependentTaskAccordian({ dependentTasks }: { dependentTasks: DependentTasks }) {
    // const [isOpen, setIsOpen] = useState(false)
    const formatTaskShortCodes = (tasks: DependentTasks) => {
        const codes = tasks.map((task) => task.short_code)

        if (codes.length === 0) return ''
        if (codes.length === 1) return codes[0]
        if (codes.length === 2) return `${codes[0]} and ${codes[1]}`

        const allButLast = codes.slice(0, -1).join(', ')
        const last = codes[codes.length - 1]
        return `${allButLast} and ${last}`
    }
    return (
        <Accordion type="single" collapsible className="w-full border-none rounded-[10px] relative">
            <AccordionItem value="item-1" className="space-y-2">
                <AccordionTrigger className="hover:no-underline cursor-pointer border p-3 max-w-fit [&>svg]:bg-[#5B687114] [&>svg]:rounded-full  [&>svg]:p-1  [&>svg]:h-[25px] [&>svg]:w-[25px]">
                    <div className="flex items-center gap-2">
                        <div>
                            <Image src={DependentTaskIcon} alt="Dependent Task Icon" width={32} className="h-auto" />
                        </div>
                        <div className="flex flex-col">
                            <p className="text-[12px] text-[#5C5F62]">{formatTaskShortCodes(dependentTasks)}</p>

                            <p className="text-xs text-[#1C1C1C66]">Dependent Tasks</p>
                        </div>
                    </div>
                </AccordionTrigger>
                <AccordionContent>
                    <div className="border bg-[#FCFCFC] rounded-md py-4 shadow-sm w-full max-w-full overflow-hidden">
                        <div className="flex mb-2 items-center gap-2 px-4">
                            <div className="border rounded-sm p-1 bg-[#F8F8F8]">
                                <Link2 size={13} color="#9B9B9B" />
                            </div>
                            <p className="text-sm text-[#09090B] font-medium">Dependent tasks Linked ({dependentTasks.length})</p>
                        </div>
                        <ScrollArea className={cn(dependentTasks?.length >= 2 ? 'h-55' : 'h-35')} type="scroll">
                            {dependentTasks.map((task) => (
                                <div
                                    className="mx-2 mb-2 p-4 border border-[#D8D8D896] bg-[#F2F2F282] rounded-[7px]"
                                    key={task.id}>
                                    <DependentTaskItem task={task} />
                                </div>
                            ))}
                        </ScrollArea>
                    </div>
                </AccordionContent>
            </AccordionItem>
        </Accordion>
    )
}

const DependentTaskItem = ({ task }: { task: DependentTaskType }) => {
    return (
        <div className="space-y-3">
            {/* Header with department chip and title */}
            <div className="flex items-center">
                {task?.taskDepartment?.label && (
                    <DepartmentChip shortCode={task?.taskDepartment?.short_code} label={task?.short_code} size="sm" />
                )}
                <span className="text-sm text-[#09090B] font-medium px-2">{task.title}</span>
            </div>

            {/* Details section */}
            <div className="space-y-3 px-2">
                {/* Assignee row */}
                <div className="flex items-start gap-4">
                    <div className="min-w-[60px]">
                        <p className="text-xs text-[#64748B] font-medium">Assignee</p>
                    </div>
                    <div className="flex-1">
                        {task?.taskAssignees?.length === 0 ? (
                            <p className="text-xs text-[#94A3B8]">No Assignees</p>
                        ) : (
                            <div className="flex items-center gap-2">
                                {task?.taskAssignees?.map((assignee) => (
                                    <div key={assignee.id} className="flex items-center gap-2">
                                        <AssigneeAvatar
                                            assignee={assignee.first_name + ' ' + assignee.last_name}
                                            imageUrl={assignee.img_url}
                                            className="h-6 w-6"
                                        />
                                        <p className="text-xs text-[#475569] font-medium">
                                            {assignee.first_name + ' ' + assignee.last_name}
                                        </p>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>

                {/* Status row */}
                <div className="flex items-center gap-4">
                    <div className="min-w-[60px]">
                        <p className="text-xs text-[#64748B] font-medium">Status</p>
                    </div>
                    <div className="flex items-center gap-2">
                        <div className="flex-shrink-0">{getStatusIcon(task.taskStatus.label)}</div>
                        <p className={cn('text-xs font-medium', getStatusStyles(task.taskStatus?.label)?.text)}>
                            {task.taskStatus.label}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    )
}
