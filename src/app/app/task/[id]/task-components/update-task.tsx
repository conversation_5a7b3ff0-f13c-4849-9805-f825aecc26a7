'use client'
import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { CreateTaskForm, type TaskFormValues } from '@/app/app/planner/create-task/create-task-form'
import { CustomModal } from '@/components/custom-modal'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import { AxiosError } from 'axios'

interface UpdateTaskModalProps {
    defaultValues: TaskFormValues & { taskId: number; shortCode: string }
    onAfterSuccess?: () => void
    isUpdateOpen: boolean
    setIsUpdateOpen: (state: boolean) => void
}

export function UpdateTaskModal({ defaultValues, onAfterSuccess, isUpdateOpen, setIsUpdateOpen }: UpdateTaskModalProps) {
    const updateTask = async (data: TaskFormValues) => {
        const body = {
            feat_id: Number(data.feature),
            title: data.taskName,
            description: data.description,
            due_date: data.dueDate,
            status: Number(data.status),
            assignee: data.teamMembers,
            department: Number(data.department),
            bug: data.isBug,
            short_code: defaultValues.shortCode,
            priority_id: Number(data.priority),
            parent_task_short_code: data.parentTaskId,
        }
        const updateEndpoint = `${endpoints.tasks.updateTask}/${defaultValues.taskId}`
        const response = await api.put(updateEndpoint, body)
        return response.data
    }

    const updateTaskMutation = useMutation({
        mutationFn: updateTask,
        onSuccess: () => {
            toast.success('Task Updated successfully')
            if (onAfterSuccess) onAfterSuccess()
            setIsUpdateOpen(false)
        },
        onError: (error: AxiosError) => {
            axiosErrorToast(error, 'Failed to update task')
        },
    })

    function onSubmit(data: TaskFormValues) {
        updateTaskMutation.mutate(data)
    }
    const closeModal = () => {
        setIsUpdateOpen?.(false)
    }

    return (
        <>
            <CustomModal isOpen={isUpdateOpen} onClose={closeModal} className="sm:max-w-[723px] max-h-[98vh]  overflow-auto">
                <div className="p-6">
                    <CreateTaskForm onSubmit={onSubmit} defaultValues={defaultValues} isUpdate />
                </div>
            </CustomModal>
        </>
    )
}
