'use client'

import { useState, useEffect, useMemo, useCallback, memo } from 'react'
import { useQuery, useMutation } from '@tanstack/react-query'
import { Flame, Loader2 } from 'lucide-react'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { AxiosError, AxiosResponse } from 'axios'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'

export interface Priority {
    id: number
    label?: string
    level?: number
}

interface PrioritySelectProps {
    initialPriorityId?: number
    fetchEndpoint: string
    updateEndpoint?: string
    className?: string
    onPriorityChange?: (newPriority: Priority) => void
    disabled?: boolean
    entityDetails?: Record<string, unknown>
    fallbackPlaceholder?: string
}

// Memoized Priority Item Component
const PriorityItem = memo(({ priority }: { priority: Priority }) => (
    <div className="flex items-center gap-2 text-sm">
        {priority.label === 'High' && <Flame className="h-4 w-3" color="#E35422" />}
        <span className={priority.label === 'High' ? 'text-[#E35422]' : ''}>{priority.label}</span>
    </div>
))
PriorityItem.displayName = 'PriorityItem'

// Memoized Selected Priority Display
const SelectedPriorityDisplay = memo(({ priority }: { priority: Priority | undefined }) => {
    if (!priority) return null

    return (
        <div className="text-xs">
            {priority.label === 'High' ? (
                <div className="flex items-center">
                    <Flame className="mr-1 h-4 w-3" color="#E35422" />
                    <span className="text-[#E35422]">{priority.label}</span>
                </div>
            ) : (
                priority.label
            )}
        </div>
    )
})
SelectedPriorityDisplay.displayName = 'SelectedPriorityDisplay'

// Memoized Loading Component
const LoadingSpinner = memo(({ className }: { className?: string }) => (
    <div
        className={cn(
            'flex h-9 items-center justify-between rounded-md border border-input bg-transparent px-3 py-2 text-sm',
            className,
        )}>
        <Loader2 className="h-4 w-4 animate-spin mr-2" />
        <span>Loading...</span>
    </div>
))
LoadingSpinner.displayName = 'LoadingSpinner'

export function PrioritySelectComponent({
    initialPriorityId,
    fetchEndpoint,
    updateEndpoint,
    className,
    onPriorityChange,
    disabled = false,
    entityDetails,
    fallbackPlaceholder = 'Priority',
}: PrioritySelectProps) {
    const [selectedPriorityId, setSelectedPriorityId] = useState<number | undefined>(initialPriorityId)

    // Memoize query key to prevent unnecessary refetches
    const queryKey = useMemo(() => ['priorities', fetchEndpoint], [fetchEndpoint])

    const { data: priorities, isLoading: isLoadingPriorities } = useQuery({
        queryKey,
        queryFn: async () => {
            const response = await api.get(fetchEndpoint)
            return response.data.data as Priority[]
        },
    })

    // Memoize mutation function to prevent recreation
    const mutationFn = useCallback(
        async (priorityId: number) => {
            if (!updateEndpoint) return
            const payload = {
                priority_id: priorityId,
                ...entityDetails,
            }
            const response = await api.put(updateEndpoint, payload)
            return response.data
        },
        [updateEndpoint, entityDetails],
    )

    const { mutate: updatePriority, isPending: isUpdating } = useMutation({
        mutationFn,
        onSuccess: useCallback(
            (data: AxiosResponse) => {
                const priorityData = data?.data
                setSelectedPriorityId(priorityData?.priority_id)
                toast.success('Priority updated successfully')
                if (onPriorityChange && priorityData) {
                    onPriorityChange(priorityData)
                }
            },
            [onPriorityChange],
        ),
        onError: useCallback((err: AxiosError) => {
            axiosErrorToast(err, 'Failed to update priority')
        }, []),
    })

    useEffect(() => {
        if (initialPriorityId !== undefined) {
            setSelectedPriorityId(initialPriorityId)
        }
    }, [initialPriorityId])

    // Memoize selected priority to prevent unnecessary recalculations
    const selectedPriority = useMemo(() => priorities?.find((p) => p.id === selectedPriorityId), [priorities, selectedPriorityId])

    // Memoize value change handler
    const handleValueChange = useCallback(
        (value: string) => {
            const numericValue = Number(value)
            setSelectedPriorityId(numericValue)

            if (updateEndpoint) {
                updatePriority(numericValue)
                return
            }

            if (onPriorityChange) {
                onPriorityChange({ id: numericValue })
            }
        },
        [updateEndpoint, updatePriority, onPriorityChange],
    )

    // Memoize string conversion for Select value
    const selectValue = useMemo(() => selectedPriorityId?.toString(), [selectedPriorityId])

    // Memoize disabled state
    const isDisabled = useMemo(() => disabled || isUpdating, [disabled, isUpdating])

    // Memoize priority items for SelectContent
    const priorityItems = useMemo(
        () =>
            priorities?.map((priority: Priority) => (
                <SelectItem key={priority.id} value={String(priority.id)}>
                    <PriorityItem priority={priority} />
                </SelectItem>
            )),
        [priorities],
    )

    if (isLoadingPriorities) {
        return <LoadingSpinner className={className} />
    }

    return (
        <Select value={selectValue} onValueChange={handleValueChange} disabled={isDisabled}>
            <div className={cn('w-full overflow-hidden flex items-center justify-center', className)}>
                <SelectTrigger className="border-none shadow-none gap-0 w-full pl-0 text-[13px]">
                    {!isUpdating ? (
                        <SelectValue placeholder={fallbackPlaceholder || 'Select priority'}>
                            {selectedPriority ? (
                                <SelectedPriorityDisplay priority={selectedPriority} />
                            ) : (
                                <>{fallbackPlaceholder}</>
                            )}
                        </SelectValue>
                    ) : (
                        <div className="w-[80px] flex justify-center">
                            <Loader2 className="h-3 w-3 animate-spin ml-2" />
                        </div>
                    )}
                </SelectTrigger>
            </div>
            <SelectContent>{priorityItems}</SelectContent>
        </Select>
    )
}

// Memoize the entire component
export const PrioritySelect = memo(PrioritySelectComponent)
