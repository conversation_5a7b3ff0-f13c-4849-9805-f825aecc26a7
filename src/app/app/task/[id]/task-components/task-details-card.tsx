'use client'

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card'
import { UserRoundPlus, FolderOpen } from 'lucide-react'
import AvatarGroup from '@/components/avatar-group'
import { formatDate } from '@/utils/format-date.utils'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'
import DepartmentChip from '@/components/ui/department-chip'
import { StatusSelect } from '@/components/select-status'
import endpoints from '@/services/api-endpoints'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'
import { useMutation } from '@tanstack/react-query'
import { PrioritySelect } from './select-task-priority'
import AddUsersModal from '@/components/add-users.modal'
import { Button } from '@/components/ui/button'
import { AxiosError } from 'axios'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'

// Define the task data structure

type TaskDepartmentType = {
    id: number
    short_code: string
    label: string
}
interface Assignee {
    id: number
    first_name: string
    last_name: string
    img_url?: string
}
type DependentTaskType = {
    id: number
    short_code: string
    title: string
    status_id: number
    taskStatus: {
        id: number
        short_code: string
        label: string
    }
    task_dependency_mapping: {
        dependency_level: number
    }
    taskDepartment?: TaskDepartmentType
}

type DependentTasks = DependentTaskType[]
export interface TaskData {
    id: number
    feat_id: number
    short_code: string
    title: string
    description: string
    time_estimate_hrs: number
    start_date: string
    due_date: string
    status_id: number
    assignee: number
    created_by: number
    created_at: string
    updatedAt: string
    deletedAt: string | null
    department: number | null
    feature: {
        id: number
        feat_code: string
        title: string
        project: {
            id: number
            name: string
        }
    }
    taskStatus: {
        id: number
        short_code: string
        label: string
        colour: string
    }
    assignedUser: Assignee
    creator: {
        id: number
        first_name: string
        last_name: string
        img_url?: string
    }
    taskDepartment: TaskDepartmentType
    priority?: string
    column?: number
    dueDate?: string
    taskPriority?: {
        id?: number
        label: string
        color?: string
    }
    dependentTasks?: DependentTasks
    bug: boolean
    project: {
        id: number
        name: string
    }
    parentTasks?: DependentTasks
    taskAssignees?:
        | [
              {
                  id: number
                  first_name: string
                  last_name: string
                  img_url?: string
              },
          ]
        | []
    isBookmarked?: boolean
}

export default function TaskDetailsCard({ taskData, refetchTask }: { taskData: TaskData; refetchTask: () => void }) {
    const handleUpdate = async (body: unknown) => {
        const updateEndpoint = `${endpoints.tasks.updateTask}/${taskData.id}`
        const response = await api.put(updateEndpoint, body)
        return response.data
    }

    const updateMutation = useMutation({
        mutationFn: handleUpdate,
    })

    const handleDueDateSelect = (date: Date) => {
        updateMutation.mutate(
            { due_date: date },
            {
                onSuccess: () => {
                    toast.success('Due date updated successfully')
                    refetchTask()
                },
                onError: (error: Error) => {
                    if (error instanceof AxiosError) {
                        axiosErrorToast(error, 'Error updating due date')
                    } else {
                        toast.error('Error updating due date')
                    }
                },
            },
        )
    }

    const handleUpdateAssignees = (users: number[], closeModal: () => void) => {
        updateMutation.mutate(
            { assignee: users },
            {
                onSuccess: () => {
                    toast.success('Assignees updated successfully')
                    refetchTask()
                    closeModal()
                },
                onError: async () => {
                    toast.error('Error updating assignees')
                },
            },
        )
    }

    return (
        <Card className="w-full max-w-[322px] shadow-none bg-transparent rounded-[10px] pt-2 gap-0">
            <CardHeader className="p-0">
                <div className="flex flex-row items-center justify-between px-4 pb-2 space-y-0 border-b">
                    <CardTitle className="text-[16px] text-[#64748B] font-medium">Task Details</CardTitle>
                </div>
            </CardHeader>
            <CardContent className="pt-0">
                <div className="space-y-3">
                    <div className="grid grid-cols-2 items-center py-1">
                        <div className="flex items-center text-sm text-[#64748B]">Status</div>
                        <div className="flex items-center">
                            <StatusSelect
                                entityId={taskData?.taskStatus.id}
                                entityType="task"
                                initialStatusId={taskData?.taskStatus?.id}
                                fetchEndpoint={endpoints.meta.getStatuses}
                                updateEndpoint={`${endpoints.tasks.updateTask}/${taskData.id}`}
                                className="w-[120px] h-[22px] rounded-[6px] border-none"
                                onStatusChange={refetchTask}
                                entityDetails={{ start_date: taskData?.start_date || new Date() }}
                            />
                        </div>
                    </div>

                    <div className="grid grid-cols-2 items-center  py-1">
                        <div className="flex items-center text-sm text-[#64748B]">Priority</div>
                        {taskData?.taskPriority && (
                            <PrioritySelect
                                initialPriorityId={taskData?.taskPriority?.id}
                                fetchEndpoint={endpoints.meta.getPriorities}
                                updateEndpoint={`${endpoints.tasks.updateTask}/${taskData.id}`}
                                className="w-[120px] h-[22px] rounded-[6px]"
                                onPriorityChange={refetchTask}
                            />
                        )}
                    </div>

                    <div className="grid grid-cols-2 items-center py-1">
                        <div className="flex items-center text-sm text-[#64748B]">Start Date</div>
                        <div className="text-sm text-[#3C557A]">
                            {taskData?.start_date ? formatDate(taskData?.start_date, 'dd Month YYYY') : 'Nil'}
                        </div>
                    </div>

                    <div className="grid grid-cols-2 items-center py-1 mb-1">
                        <div className="flex items-center text-sm text-[#64748B]">Due Date</div>
                        <div className="text-sm">
                            <Popover>
                                <PopoverTrigger className="cursor-pointer border-b-1 border-[#3C557A] text-[#3C557A] text-sm">
                                    {taskData?.due_date ? formatDate(taskData?.due_date, 'dd Month YYYY') : 'Nil'}
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0" align="start">
                                    <Calendar
                                        mode="single"
                                        disabled={(date) => date < new Date()}
                                        selected={new Date(taskData?.due_date)}
                                        onSelect={(date) => handleDueDateSelect(date as Date)}
                                        initialFocus
                                    />
                                </PopoverContent>
                            </Popover>
                        </div>
                    </div>

                    <div className="grid grid-cols-2 items-center py-0 mb-1">
                        <div className="flex items-center text-sm text-[#64748B]">Assignee</div>
                        <div className="flex items-center justify-between">
                            <AvatarGroup team={taskData?.taskAssignees} className="h-[28px] w-[28px]" />
                            <AddUsersModal
                                project_id={taskData?.project?.id || taskData?.feature?.project?.id}
                                alreadyAssignedUsers={taskData?.taskAssignees?.map((assignee) => assignee.id)}
                                onSubmit={(users, closeModal) => handleUpdateAssignees(users, closeModal)}
                                dialogueTitle="Add Assignees"
                                dialogueDescription="Add assignees to the task.">
                                <Button variant="link" className="text-[#3C557A]">
                                    <UserRoundPlus className="h-4 w-4" color="#939393" />
                                </Button>
                            </AddUsersModal>
                        </div>
                    </div>

                    <div className="grid grid-cols-2 items-center  py-1">
                        <div className="flex items-center text-sm text-[#64748B]">Project</div>
                        <div className="flex items-center gap-2">
                            <div className="p-1 rounded-[8px] border bg-white">
                                <FolderOpen size={13} color="#9BA5B1" />
                            </div>
                            <div className="text-sm text-[#3C557A] line-clamp-2" title={taskData?.feature?.project?.name}>
                                {taskData?.feature?.project?.name}
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-2 items-center py-1">
                        <div className="flex items-center text-sm text-[#64748B]">Feature</div>
                        <div className="text-sm text-[#3C557A] line-clamp-2" title={taskData?.feature?.title}>
                            {taskData?.feature?.title}
                        </div>
                    </div>

                    <div className="grid grid-cols-2 items-center py-1">
                        <div className="flex items-center text-sm text-[#64748B]">Type</div>
                        {taskData?.taskDepartment && (
                            <div>
                                <DepartmentChip
                                    shortCode={taskData?.taskDepartment?.short_code}
                                    label={taskData?.taskDepartment?.label}
                                    size="sm"
                                />
                            </div>
                        )}
                    </div>

                    <div className="grid grid-cols-2 items-center py-1">
                        <div className="flex items-center text-sm text-[#64748B]">Creator</div>
                        <div className="flex items-center gap-2">
                            <AssigneeAvatar
                                assignee={taskData?.creator?.first_name + ' ' + taskData?.creator?.last_name}
                                imageUrl={taskData?.creator?.img_url}
                            />
                            <span className="text-sm text-[#3C557A]">
                                {taskData?.creator?.first_name + ' ' + taskData?.creator?.last_name}
                            </span>
                        </div>
                    </div>

                    <div className="grid grid-cols-2 items-center py-1">
                        <div className="flex items-center text-sm text-[#64748B]">Created on</div>
                        <div className="text-sm text-[#3C557A] line-clamp-2">
                            {taskData?.created_at ? formatDate(taskData?.created_at, 'dd Month YYYY') : 'Nil'}
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    )
}
