import AvatarGroup from '@/components/avatar-group'
import { DeleteWithAlert } from '@/components/delete-with-alert-dialog'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import endpoints from '@/services/api-endpoints'
import { Trash2 } from 'lucide-react'
import { useState } from 'react'
import UpdateWorkspaceModal from './update-worspace-modal'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'

type WorkspaceType = {
    id: number
    name: string
    img_url: string
    invite_link: string
    plan: number
    created_at: string
    members: {
        id: number
        first_name: string
        last_name: string
        img_url: string
    }[]
    admins: {
        id: number
        first_name: string
        last_name: string
        img_url: string
    }[]
}

const WorkspaceCard = ({
    workspace,
    onAfterWorkspaceDelete,
    refetchWorkspaces,
}: {
    workspace: WorkspaceType
    onAfterWorkspaceDelete: () => void
    refetchWorkspaces: () => void
}) => {
    const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false)
    const handleEditClick = () => {
        setIsUpdateModalOpen(true)
    }

    return (
        <>
            <Card className="pl-8">
                <div className="flex pr-6">
                    <div className="flex flex-col gap-2 flex-1 py-4">
                        <div className="flex justify-between items-center">
                            <div className="flex gap-2 items-center">
                                <div className="w-[40px] h-[40px] flex justify-center items-center rounded-[8px] bg-gray-200 border-2 border-white overflow-hidden">
                                    {workspace.img_url && (
                                        //AssigneeAvatar can use to show images bcz the url not working on Image in prod
                                        <AssigneeAvatar
                                            showTooltip={false}
                                            assignee={workspace.name}
                                            imageUrl={workspace.img_url}
                                            className="h-[32px] w-[32px]"
                                        />
                                    )}
                                </div>
                                <div className="flex flex-col">
                                    <div className="text-sm font-medium">{workspace.name}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <DeleteWithAlert
                            title="Are you sure you want to delete this workspace?"
                            description="This action cannot be undone."
                            endpoint={`${endpoints.workspace.deleteWorkspace.replace(':id', workspace.id.toString())}`}
                            onAfterSuccess={onAfterWorkspaceDelete}>
                            <div className="text-xs text-[#3C557A] bg-white border p-[10px] rounded-md">
                                <Trash2 size={14} color="#9B9B9B" />
                            </div>
                        </DeleteWithAlert>
                        <Button variant="outline" className=" text-xs text-[#0F172A]" onClick={handleEditClick}>
                            Edit Workspace
                        </Button>
                    </div>
                </div>
                <div className="flex">
                    <div className="text-xs text-[#3C557A] w-40">Team Members</div>
                    <AvatarGroup team={workspace.members} />
                </div>
                <div className="flex">
                    <div className="text-xs text-[#3C557A] w-40">Date Created</div>
                    <div className="text-xs text-[#3C557A]">
                        {new Date(workspace.created_at).toLocaleDateString('en-US', {
                            month: '2-digit',
                            day: '2-digit',
                            year: 'numeric',
                        })}
                    </div>
                </div>
                <div className="flex">
                    <div className="text-xs text-[#3C557A] w-40">Admin</div>
                    <div className="flex items-center gap-2">
                        <AvatarGroup team={workspace.admins} />
                    </div>
                </div>
            </Card>

            <UpdateWorkspaceModal
                open={isUpdateModalOpen}
                onOpenChange={setIsUpdateModalOpen}
                workspaceData={workspace}
                refetchWorkspaces={refetchWorkspaces}
            />
        </>
    )
}

export default WorkspaceCard
