import { useAuthStore } from '@/store/auth.store'
import WorkspaceCard from '../workspace-card'
import { useQuery } from '@tanstack/react-query'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { toast } from 'sonner'
import { Loader2 } from 'lucide-react'

type WorkspaceType = {
    id: number
    name: string
    img_url: string
    invite_link: string
    plan: number
    created_at: string
    members: {
        id: number
        first_name: string
        last_name: string
        img_url: string
    }[]
    admins: {
        id: number
        first_name: string
        last_name: string
        img_url: string
    }[]
}

const WorkspaceTabContent = () => {
    const { user } = useAuthStore()
    const userId = user?.id
    const { fetchMyDetails } = useAuthStore()

    const {
        data: workspaces,
        isLoading,
        refetch,
    } = useQuery({
        queryKey: ['workspaces', userId],
        queryFn: async () => {
            try {
                const response = await api.get(endpoints.workspace.getWorkspaces)
                return response.data.data
            } catch (error) {
                toast.error('Failed to fetch workspaces')
                throw error
            }
        },
        enabled: !!userId,
    })
    const handleWorkspaceDeleteSuccess = () => {
        refetch()
        toast.success('Workspace deleted successfully')
        fetchMyDetails()
    }

    if (isLoading)
        return (
            <div className="flex h-[300px] flex-col items-center justify-center w-full text-center">
                <Loader2 className="h-6 w-6 animate-spin " />
            </div>
        )

    return (
        <div className="space-y-4">
            {workspaces?.map((workspace: WorkspaceType) => (
                <WorkspaceCard
                    key={workspace.id}
                    workspace={workspace}
                    onAfterWorkspaceDelete={handleWorkspaceDeleteSuccess}
                    refetchWorkspaces={refetch}
                />
            ))}
        </div>
    )
}

export default WorkspaceTabContent
