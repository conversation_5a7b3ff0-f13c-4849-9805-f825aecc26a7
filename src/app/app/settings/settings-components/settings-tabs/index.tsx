'use client'

import React from 'react'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { useTabStore } from '@/store/tabs.store'
import ProfileTabContent from './profile-tab-content'
import { useAuthStore } from '@/store/auth.store'
import WorkspaceTabContent from './workspace-tab-content'
import SubscriptionTabContent from './subscription-tab-content'

interface TabItem {
    id: string
    label: string
    icon?: React.ReactNode
    content: React.ReactNode
    count?: number
}

export default function SettingsTabs() {
    const { settingsActiveTab, setSettingsActiveTab } = useTabStore()
    const { user } = useAuthStore()

    // Create an array of tab items with optional count badges
    const tabItems: TabItem[] = [
        {
            id: 'profile',
            label: 'Profile',
            content: <ProfileTabContent />,
        },
        {
            id: 'workspace',
            label: 'Workspace',
            content: <WorkspaceTabContent />,
            count: user?.workspaces?.length,
        },
        {
            id: 'subscription',
            label: 'Subscription',
            content: <SubscriptionTabContent />,
        },
        {
            id: 'notifications',
            label: 'Notifications',
            content: <></>,
        },
    ]

    return (
        <div className="flex rounded-lg bg-transparent pb-4 items-center">
            <Tabs value={settingsActiveTab} onValueChange={setSettingsActiveTab} className="w-full">
                <div className="flex flex-row justify-between items-center w-full pb-3  border-b-2">
                    <TabsList className="grid gap-2 grid-cols-5 h-full bg-transparent p-0">
                        {tabItems.map((tab) => (
                            <TabsTrigger
                                key={tab.id}
                                value={tab.id}
                                className=" relative p-0 data-[state=active]:shadow-none data-[state=active]:text-[#2FA87D]  data-[state=inactive]:text-[#575757] cursor-pointer">
                                <span>{tab.label}</span>
                                {tab.count && <span className="bg-[#F2F4F7]  rounded-full px-2 py-1 text-xs">{tab.count}</span>}
                                {tab.id === settingsActiveTab && (
                                    <div className="border-b-2 border-[#2FA87D] absolute bottom-[-15px] w-full" />
                                )}
                            </TabsTrigger>
                        ))}
                    </TabsList>
                </div>
                {tabItems.map((tab) => (
                    <TabsContent key={tab.id} value={tab.id}>
                        {tab.content}
                    </TabsContent>
                ))}
            </Tabs>
        </div>
    )
}
