import React, { useEffect, useState } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import endpoints from '@/services/api-endpoints'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'
import { useAuthStore } from '@/store/auth.store'
import { Form, FormField } from '@/components/ui/form'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FormInputField } from '@/components/form-fields'
import { Button } from '@/components/ui/button'
import Image from 'next/image'
import { Loader, Upload } from 'lucide-react'
import { Textarea } from '@/components/ui/textarea'
import { cn } from '@/lib/utils'
import { AxiosError } from 'axios'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import { uploadFile } from '@/lib/minio-client'
import { PaginatedSelect } from '@/components/paginated-select'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'

// 1. Zod Schema
const profileSchema = z.object({
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    email: z.string().email('Invalid email address'),
    password: z.string().optional(),
    img_url: z.string().optional(),
    designation: z.number().optional(),
    country: z.number().optional(),
    bio: z.string().optional(),
})

type ProfileFormValues = z.infer<typeof profileSchema>
type FileUploadResult = {
    success: boolean
    fileUrl?: string | null
}

export default function ProfileTabContent() {
    const userId = useAuthStore((state) => state.user?.id)
    const refetchUserDetails = useAuthStore((state) => state.fetchMyDetails)
    const [logoImage, setLogoImage] = useState<File | null>(null)
    const [isEditMode, setIsEditMode] = useState(false)
    const [isFormReset, setIsFormReset] = useState(false)
    const [imageUrl, setImageUrl] = useState<string | null>(null)

    const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            setLogoImage(e.target.files[0])
        }
    }

    const removeImage = () => setLogoImage(null)

    const {
        data: userData,
        isLoading,
        refetch,
    } = useQuery({
        queryKey: ['user', userId],
        queryFn: async () => {
            try {
                const url = endpoints.user.getUserById.replace(':id', userId?.toString() || '')
                const response = await api.get(url)
                return response.data.data
            } catch (error) {
                toast.error('Failed to fetch user data')
                throw error
            }
        },
        enabled: !!userId,
    })

    const form = useForm<ProfileFormValues>({
        resolver: zodResolver(profileSchema),
        defaultValues: {
            firstName: '',
            lastName: '',
            email: '',
            password: 'password',
            img_url: '',
            designation: undefined,
            country: undefined,
            bio: '',
        },
    })

    // 3. Reset form with fetched data
    useEffect(() => {
        if (userData) {
            form.reset({
                firstName: userData?.first_name || '',
                lastName: userData?.last_name || '',
                email: userData.email || '',
                img_url: userData.img_url || '',
                designation: userData.team_member_role?.id || undefined,
                country: userData.timezone?.id || undefined,
                bio: userData.bio || '',
            })
            setImageUrl(userData.img_url || null)
        }
        return () => {
            form.reset()
            setImageUrl(null)
        }
    }, [userData, form])

    const mutation = useMutation({
        mutationFn: async (data: ProfileFormValues) => {
            const payload = {
                firstName: data.firstName,
                lastName: data.lastName,
                email: data.email,
                role: data.designation,
                timezoneId: data.country,
                bio: data.bio,
                imageUrl: data.img_url,
            }
            let result: FileUploadResult = { success: false, fileUrl: '' } // Initialize result

            if (logoImage) {
                const formData = new FormData()
                formData.append('file', logoImage)
                result = await uploadFile(formData)
            }

            if (logoImage && !result.success) {
                throw new Error('Upload failed')
            }
            if (result.fileUrl) {
                payload.imageUrl = result.fileUrl
            }
            const url = endpoints.user.updateUserById.replace(':id', userId?.toString() || '')
            const response = await api.patch(url, payload)
            return response.data
        },
        onSuccess: () => {
            toast.success('Profile updated successfully!')
            setIsEditMode(false)
            refetch()
            refetchUserDetails()
        },
        onError: (error: AxiosError) => {
            axiosErrorToast(error, 'Failed to update profile. Please try again.')
        },
    })

    const onSubmit = (data: ProfileFormValues) => {
        mutation.mutate(data)
    }

    const resetForm = () => {
        form.reset()
        removeImage()
        setIsEditMode(false)
        setIsFormReset(true)
    }
    if (isLoading)
        return (
            <div className="flex items-center justify-center h-[88dvh]">
                <Loader className="animate-spin" />
            </div>
        )

    const labelStyle = 'text-[#344054] text-sm font-medium'

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="flex justify-between py-4 border-b">
                    <div>
                        <p className="text-[17px] text-[#101828] font-medium">Personal info</p>
                        <p className="text-xs text-[#667085]">Update your photo and personal details here.</p>
                    </div>
                    <div className="flex gap-2">
                        {isEditMode ? (
                            <>
                                <Button
                                    variant="outline"
                                    type="button"
                                    onClick={(e) => {
                                        e.preventDefault()
                                        resetForm()
                                    }}>
                                    Cancel
                                </Button>
                                <Button type="submit">Save</Button>
                            </>
                        ) : (
                            <Button
                                onClick={() => {
                                    setIsEditMode(true)
                                    setIsFormReset(false)
                                }}>
                                Edit Profile
                            </Button>
                        )}
                    </div>
                </div>

                <div className="grid grid-cols-3 gap-2 items-center pb-6 border-b">
                    <div className={labelStyle}>Name</div>
                    <div className="grid grid-cols-2 gap-2">
                        <FormField
                            control={form.control}
                            name="firstName"
                            render={({ field }) => (
                                <FormInputField
                                    label=""
                                    placeholder="Enter your first name"
                                    field={field}
                                    itemClassName="w-full"
                                    inputClassName={cn('bg-white rounded-sm')}
                                    readOnly={!isEditMode}
                                />
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="lastName"
                            render={({ field }) => (
                                <FormInputField
                                    label=""
                                    placeholder="Enter your last name"
                                    field={field}
                                    itemClassName="w-full"
                                    inputClassName={cn('bg-white rounded-sm')}
                                    readOnly={!isEditMode}
                                />
                            )}
                        />
                    </div>
                </div>

                <div className="pb-6 border-b">
                    <div className="grid grid-cols-3 gap-2 items-center">
                        <div className={labelStyle}>Email</div>
                        <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                                <FormInputField
                                    label=""
                                    placeholder="Enter your email"
                                    field={field}
                                    itemClassName="w-full"
                                    inputClassName={cn('bg-[#F9FAFB] rounded-sm rounded-sm')}
                                    readOnly
                                />
                            )}
                        />
                    </div>
                    <div className="grid grid-cols-3 gap-2 items-center">
                        <div className={labelStyle}>Password</div>
                        <FormField
                            control={form.control}
                            name="password"
                            render={({ field }) => (
                                <FormInputField
                                    label=""
                                    placeholder="Enter your password"
                                    field={field}
                                    itemClassName="w-full"
                                    inputClassName={cn('bg-[#F9FAFB] rounded-sm rounded-sm')}
                                    type="password"
                                    readOnly
                                />
                            )}
                        />
                        <div className="text-[#667085] text-center text-sm font-semibold cursor-pointer w-full">
                            Change Password
                        </div>
                    </div>
                </div>

                <div className="grid grid-cols-3 gap-2 items-center border-b pb-6">
                    <div>
                        <p className={labelStyle}>Your Photo</p>
                        <p className="text-[#667085] text-sm">This will be displayed on your profile.</p>
                    </div>

                    <div className="w-full">
                        <div className="flex items-center space-x-3">
                            <div className="relative w-12 h-12 rounded-full border overflow-hidden bg-[#08B38B0D] flex items-center justify-center">
                                {imageUrl && !logoImage && (
                                    <AssigneeAvatar
                                        assignee={userData?.first_name + ' ' + userData?.last_name}
                                        imageUrl={imageUrl}
                                        className="h-[48px] w-[48px]"
                                    />
                                )}
                                {logoImage && (
                                    <Image
                                        src={URL.createObjectURL(logoImage)}
                                        alt="Workspace Logo"
                                        width={48}
                                        height={48}
                                        className="object-cover"
                                    />
                                )}
                            </div>
                            <div>
                                <div className="flex items-center space-x-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className={cn('text-sm font-semibold bg-[#F4F4F5] shadow-none border-none')}
                                        asChild
                                        disabled={!isEditMode}>
                                        <label>
                                            Upload Image
                                            <input
                                                type="file"
                                                className="hidden"
                                                accept="image/*"
                                                onChange={handleImageUpload} // Handle file selection
                                                disabled={!isEditMode}
                                            />
                                            <Upload className="ml-1 h-3 w-3" />
                                        </label>
                                    </Button>
                                    {/* <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        className="text-xs text-[#09090B]"
                                        onClick={removeImage}
                                        disabled={!logoImage}>
                                        Remove
                                    </Button> */}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="grid grid-cols-3 gap-2 items-center border-b pb-6">
                    <div className={labelStyle}>Designation</div>
                    {/* <FormField
                        control={form.control}
                        name="designation"
                        render={({ field }) => (
                            <FormInputField
                                label=""
                                placeholder="Enter your desgnation"
                                field={field}
                                itemClassName="w-full"
                                inputClassName={cn('bg-[#F9FAFB] rounded-sm rounded-sm')}
                                readOnly={!isEditMode}
                            />
                        )}
                    /> */}
                    <FormField
                        control={form.control}
                        name="designation"
                        render={({ field }) => (
                            <PaginatedSelect
                                endpoint={endpoints.meta.getDesignations}
                                onSelectionChange={(value) => {
                                    field.onChange(value[0]?.id)
                                }}
                                placeholder="Select your desgnation"
                                returnKeys={['id']}
                                labelKey="role"
                                enableSearch={false}
                                defaultValueLabel={userData?.team_member_role?.role || ''}
                                disabled={!isEditMode}
                                resetTrigger={isFormReset}
                            />
                        )}
                    />
                </div>

                <div className="grid grid-cols-3 gap-2 items-center border-b pb-6">
                    <div className={labelStyle}>Country</div>
                    <FormField
                        control={form.control}
                        name="country"
                        render={({ field }) => (
                            <PaginatedSelect
                                endpoint={endpoints.meta.getTimezones}
                                onSelectionChange={(value) => {
                                    field.onChange(value[0]?.id)
                                }}
                                placeholder="Select your country"
                                returnKeys={['id']}
                                labelKey="display_name"
                                enableSearch={false}
                                defaultValueLabel={userData?.timezone?.display_name || ''}
                                disabled={!isEditMode}
                                resetTrigger={isFormReset}
                            />
                        )}
                    />
                </div>

                <div className="grid grid-cols-3 gap-2  border-b pb-6">
                    <div className={labelStyle}>Bio</div>
                    <FormField
                        control={form.control}
                        name="bio"
                        render={({ field }) => (
                            <Textarea
                                {...field}
                                placeholder="Enter your bio"
                                className={cn('w-full bg-white rounded-sm min-h-[128px]')}
                                rows={10}
                                readOnly={!isEditMode}
                            />
                        )}
                    />
                </div>
            </form>
        </Form>
    )
}
