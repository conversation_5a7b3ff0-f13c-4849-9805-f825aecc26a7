import { PurchaseModal } from '@/components/purchase-modal'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { useTabStore } from '@/store/tabs.store'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import { formatDate } from '@/utils/format-date.utils'
import { useQuery } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { Calendar, Coins, Loader, Plus } from 'lucide-react'
import React from 'react'

const billingInfo = {
    nextPayment: '2025-10-2',
    charge: 100,
}

const SubscriptionTabContent = () => {
    const { settingsActiveTab } = useTabStore()

    const { data: creditUsage, isLoading } = useQuery({
        queryKey: ['creditUsage'],
        queryFn: async () => {
            try {
                const response = await api.get(endpoints.llm.getCredits)
                return response.data.data
            } catch (error: unknown) {
                axiosErrorToast(error as AxiosError, 'Failed to fetch credit usage')
                throw error
            }
        },
        enabled: settingsActiveTab === 'subscription',
    })
    const usedCredit = Number(creditUsage?.totalCredit) - Number(creditUsage?.creditsRemaining)
    const usageInfo = {
        total: Number(creditUsage?.totalCredit),
        used: usedCredit,
        remaining: creditUsage?.creditsRemaining,
        quotaPerMonth: Number(creditUsage?.totalCredit),
    }

    if (isLoading) {
        return (
            <div className="flex h-[300px] flex-col items-center justify-center w-full text-center">
                <div className="flex h-full items-center justify-center">
                    <Loader className="h-6 w-6 animate-spin text-gray-400" />
                </div>
            </div>
        )
    }
    return (
        <>
            <p className="text-[17px] text-[#101828] font-medium">Subscription</p>
            <p className="text-xs text-[#667085]">Manage your subscription and billing details.</p>
            <div className="flex w-[75%] gap-4 mt-4 h-[400px] items-start">
                <Card className="w-1/2 shadow-none p-5">
                    <div className="flex justify-between items-center">
                        <p className="text-[#000] text-[17px] font-medium">User Message</p>
                        <p className="text-[#2c78e9] text-xs cursor-pointer">View usage</p>
                    </div>
                    <div className="flex justify-start gap-4">
                        <div className="rounded-full bg-[#1D1D1D0A] flex justify-center items-center w-[40px] h-[40px]">
                            <Coins className="h-4 w-4 text-[#6C7491] cursor-pointer " />
                        </div>
                        <div>
                            <p className="text-[#000] text-xl font-medium">{usageInfo.remaining} available</p>
                            <p className="text-[#667085] text-xs">{usageInfo.quotaPerMonth} renew monthly</p>
                        </div>
                    </div>
                    <div>
                        <Progress value={usageInfo.used} className="h-2 w-full mx-auto" />
                        <p className=" text-xs text-[#667085] pt-2">
                            Used {usageInfo.used} of {usageInfo.total} this month
                        </p>
                    </div>
                    <PurchaseModal>
                        <Button className="w-full bg-[#bfefd1] text-[#096f04] rounded-sm p-2 text-sm hover:bg-[#9cdfb5]">
                            <Plus className="mr-2 h-4 w-4" /> Purchase additional user messages
                        </Button>
                    </PurchaseModal>
                </Card>
                <Card className="w-1/2 shadow-none p-5">
                    <div className="flex justify-between items-center ">
                        <p className="text-[#000] text-[17px] font-medium">Billing</p>
                        <p className="text-[#2c78e9] text-xs cursor-pointer">Payment History</p>
                    </div>
                    <div className="flex justify-start gap-4">
                        <div className="rounded-full bg-[#1D1D1D0A] flex justify-center items-center w-[40px] h-[40px]">
                            <Calendar className="h-4 w-4 text-[#6C7491] cursor-pointer " />
                        </div>
                        <div>
                            <p className="text-[#000] text-xl font-medium">
                                {formatDate(billingInfo.nextPayment, 'Month dd, YYYY')}{' '}
                            </p>
                            <p className="text-[#667085] text-xs">Next Billing Date</p>
                        </div>
                    </div>
                    <p className=" text-xs text-[#667085] pt-2">Your card will be charged ${billingInfo.charge}</p>
                    <Button className="w-full rounded-sm p-2 text-sm mt-2">Update payment method & billing info</Button>
                </Card>
            </div>
        </>
    )
}

export default SubscriptionTabContent
