'use client'

import { FC, useRef, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import ProjectCard from '@/components/project-card'
import { Loader } from 'lucide-react'

import { useInfiniteQuery } from '@tanstack/react-query'
import { toast } from 'sonner'
import endpoints from '@/services/api-endpoints'
import { api } from '@/config/axios-config'
import { useAuthStore } from '@/store/auth.store'
import CreateProjectModal from '@/components/create-project-modal'
import { ProjectCount } from './page'
import EmptyState from '@/components/empty-projects'

interface ProjectsGridProps {
    status?: string
    search?: string
    viewType?: 'grid' | 'list'
    debouncedSearchQuery: string
    handleCloseModal: () => void
    isModalOpen: boolean
    handleProjectCount: (data: ProjectCount) => void
    handleCreateProject: () => void
}

const ProjectsGridView: FC<ProjectsGridProps> = ({
    debouncedSearchQuery,
    handleCloseModal,
    isModalOpen,
    handleProjectCount,
    handleCreateProject,
}) => {
    const router = useRouter()
    const parentRef = useRef<HTMLDivElement>(null)
    const currentWorkspace = useAuthStore((state) => state.currentWorkspace)

    const { data, isLoading, isError, refetch, fetchNextPage, hasNextPage, isFetchingNextPage } = useInfiniteQuery({
        queryKey: ['projects', { searchQuery: debouncedSearchQuery, workspaceId: currentWorkspace?.id }],
        initialPageParam: 1,
        queryFn: async ({ pageParam }) => {
            if (!currentWorkspace?.id)
                return {
                    projectData: [],
                    paginationData: {
                        total: 0,
                        currentPage: 1,
                        totalPages: 1,
                        pageSize: 10,
                    },
                }

            try {
                const response = await api.get(endpoints.project.listProjects, {
                    params: {
                        workspace_id: currentWorkspace.id,
                        searchQuery: debouncedSearchQuery,
                        page: pageParam,
                        limit: 10,
                    },
                })
                return response.data.data
            } catch (error) {
                console.error('Error fetching projects:', error)
                toast.error('Error fetching projects. Please try again later.')
                throw error
            }
        },
        getNextPageParam: (lastPage) => {
            if (lastPage?.paginationData?.currentPage < lastPage?.paginationData?.totalPages) {
                return lastPage?.paginationData?.currentPage + 1
            }
            return undefined
        },
        getPreviousPageParam: (firstPage) => {
            if (firstPage?.paginationData?.currentPage > 1) {
                return firstPage?.paginationData?.currentPage - 1
            }
            return undefined
        },
        // staleTime: 5 * 60 * 1000,
    })

    // Combine all projects from all pages
    const allProjects = data ? data.pages.flatMap((page) => page.projectData) : []
    useEffect(() => {
        if (data) {
            const latestPage = data.pages[data.pages.length - 1]
            const totalLoaded = data.pages.flatMap((page) => page.projectData).length

            handleProjectCount({
                ...latestPage.paginationData,
                loaded: totalLoaded,
            })
        }
    }, [data, handleProjectCount])
    const handleProjectClick = (projectId: string) => {
        router.push(`/app/projects/${projectId}`)
    }

    useEffect(() => {
        const scrollContainer = parentRef.current
        if (!scrollContainer) return

        const handleScroll = () => {
            const { scrollTop, scrollHeight, clientHeight } = scrollContainer

            // When user has scrolled to bottom (with a small threshold)
            if (scrollHeight - scrollTop - clientHeight < 50) {
                if (hasNextPage && !isFetchingNextPage) {
                    fetchNextPage()
                }
            }
        }

        scrollContainer.addEventListener('scroll', handleScroll)
        return () => {
            scrollContainer.removeEventListener('scroll', handleScroll)
        }
    }, [hasNextPage, isFetchingNextPage, fetchNextPage])

    if (isLoading && allProjects.length === 0) {
        return (
            <div className="w-full h-full flex items-center justify-center">
                <Loader className="animate-spin" size={15} />
            </div>
        )
    }

    if (isError) {
        return (
            <div className="p-6 text-center">
                <p className="text-red-500">Failed to load projects. Please try again later.</p>
                <button onClick={() => refetch()} className="mt-4 px-4 py-2 bg-black text-white rounded hover:bg-gray-800">
                    Retry
                </button>
            </div>
        )
    }

    if (allProjects.length === 0) {
        return (
            <>
                <EmptyState onClick={handleCreateProject} />
                <CreateProjectModal isOpen={isModalOpen} onClose={handleCloseModal} reFetchProjectList={refetch} />
            </>
        )
    }

    return (
        <div ref={parentRef} className="max-h-[80vh] pr-4 overflow-auto mt-2 custom-scroll">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mt-4">
                {allProjects?.map((project) => (
                    <ProjectCard key={project.id} project={project} onClick={handleProjectClick} />
                ))}
            </div>

            {isFetchingNextPage && (
                <div className="flex justify-center p-4">
                    <Loader className="animate-spin" size={15} />
                </div>
            )}

            {!hasNextPage && allProjects.length > 10 && (
                <div className="text-center text-xs text-gray-500 p-4">No more projects to load</div>
            )}
            <CreateProjectModal isOpen={isModalOpen} onClose={handleCloseModal} reFetchProjectList={refetch} />
        </div>
    )
}

export default ProjectsGridView
