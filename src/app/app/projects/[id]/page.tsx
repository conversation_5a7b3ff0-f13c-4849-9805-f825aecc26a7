'use client'

import { useEffect } from 'react'

import { useBreadcrumbStore } from '@/store/breadcrumb.store'
import { ProjectProgressDetails } from './project-details'
import ProjectTabs from './bottom-tab-section'
import { useQuery } from '@tanstack/react-query'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'
import { useParams } from 'next/navigation'
import endpoints from '@/services/api-endpoints'
import { Bug, CircleCheck, Loader, OctagonAlert, Puzzle } from 'lucide-react'
import TaskDistributionGraph from './task-distribution-graph'
import Comments from '@/components/comments'
import { DepartmentType } from '@/components/project-card'

export type ProjectType = {
    id: number
    name: string
    overview: string
    workspace_id: number
    status_id: number
    stack: Array<string>
    created_at: string
    updatedAt: string
    deletedAt: null
    created_by: number
    creator: {
        id: number
        first_name: string
        last_name: string
        img_url?: string
    }
    status: {
        id: number
        short_code: string
        label: string
        colour: string
    }
    features: {
        completed: number
        total: number
    }
    departments: DepartmentType[]
    team: Array<{
        id: number
        first_name: string
        last_name: string
        avatar?: string
    }>
    estimated_effort: string
    timeline: {
        start_date: string
        end_date: string
        days: number
    }
    priority: {
        level: string
        color: string
    }
    progress: {
        percentage: number
        completed: number
        total: number
    }
    task_stats: {
        completed_tasks: number
        delayed_tasks: number
        open_bugs: number
    }
}

export default function Page() {
    const { setBreadcrumbs } = useBreadcrumbStore()

    const params = useParams()
    const projectId = params.id

    // Fetch project data
    const {
        data: project,
        isLoading,
        refetch,
    } = useQuery({
        queryKey: ['project', projectId],
        queryFn: async () => {
            try {
                const response = await api.get(`${endpoints.project.listProjects}/${projectId}`)
                return response.data.data
            } catch (error) {
                toast.error('Failed to fetch project details')
                throw error
            }
        },
        enabled: !!projectId,
    })

    useEffect(() => {
        setBreadcrumbs([
            { label: 'Dashboard', href: '/app' },
            { label: 'Projects', href: '/app/projects' },
            { label: project?.name || 'Project Name', href: `/app/projects/${projectId}` },
        ])
    }, [setBreadcrumbs, project?.name, projectId])

    if (isLoading) {
        return (
            <div className="w-full flex items-center min-h-screen justify-center shadow-none border-none bg-transparent pt-0">
                <Loader className="animate-spin" />
            </div>
        )
    }

    return (
        <>
            <div className="flex flex-col lg:flex-row w-full gap-4 mb-4">
                <div className="w-full lg:w-[70%] space-y-4 border-b lg:border-b-0 lg:border-r pr-0 lg:pr-4 pb-4 lg:pb-0">
                    <div>
                        <ProjectProgressDetails project={project} />
                        <ProjectTabs project={project} refetchProject={refetch} />
                    </div>
                </div>
                <div className="w-full lg:w-[30%] space-y-4">
                    <ProjectCounts project={project} />
                    <div className="border rounded-[8px] w-full">
                        <TaskDistributionGraph />
                    </div>
                </div>
            </div>
            <div className="bg-[#F4F4F570] rounded-[8px] p-4">
                <div className="w-1/2">
                    <Comments entity_id={projectId as string} entity_type="project" />
                </div>
            </div>
        </>
    )
}

const ProjectCounts = ({ project }: { project: ProjectType }) => {
    const commonCardStyles = 'border rounded-[8px] p-3 md:p-4 shadow-xs font-medium'
    const countStyle = 'text-sm mt-2 md:mt-4'

    return (
        <div className="grid grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-2 md:gap-4 mb-4 text-sm">
            <div className={commonCardStyles}>
                <div className="flex items-center gap-2 text-[#7E7E80]">
                    <Puzzle size={14} />
                    <p className="text-xs md:text-sm">Features</p>
                </div>
                <p className={countStyle}>{project?.features?.total}</p>
            </div>
            <div className={commonCardStyles}>
                <div className="flex items-center gap-2 text-[#28A745]">
                    <CircleCheck size={14} />
                    <p className="text-xs md:text-sm">Completed Tasks</p>
                </div>
                <p className={countStyle}>{project?.task_stats?.completed_tasks}</p>
            </div>
            <div className={commonCardStyles}>
                <div className="flex items-center gap-2 text-[#EC6A5B]">
                    <OctagonAlert size={14} />
                    <p className="text-xs md:text-sm">Delayed Tasks</p>
                </div>
                <p className={countStyle}>{project?.task_stats?.delayed_tasks}</p>
            </div>
            <div className={commonCardStyles}>
                <div className="flex items-center gap-2 text-[#FF6D00]">
                    <Bug size={14} />
                    <p className="text-xs md:text-sm">Open Bugs</p>
                </div>
                <p className={countStyle}>{project?.task_stats?.open_bugs}</p>
            </div>
        </div>
    )
}
