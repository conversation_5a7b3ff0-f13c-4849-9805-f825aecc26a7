'use client'

import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Puzzle, Logs, CircleAlert, PlusCircle, Trash2 } from 'lucide-react'
import ProjectDetailsTab from './details'
import FeaturesTab from './features'
import ActivityLog from './activity-log'
import { Button } from '@/components/ui/button'
import { useTabStore } from '@/store/tabs.store'
import CreateFeatureModal from './modals/create-feature-modal'
import { useParams, useRouter } from 'next/navigation'
import { useQueryClient } from '@tanstack/react-query'
import { DeleteWithAlert } from '@/components/delete-with-alert-dialog'
import endpoints from '@/services/api-endpoints'
import { toast } from 'sonner'
import { ProjectType } from '../page'
import { invalidateActivityLogs } from '@/services/invalidate-query.service'

interface TabItem {
    id: string
    label: string
    icon?: React.ReactNode
    content: React.ReactNode
    count?: number
}

export default function ProjectBottomTabs({ project, refetchProject }: { project: ProjectType; refetchProject: () => void }) {
    const { projectActiveTab, setProjectActiveTab } = useTabStore()
    const router = useRouter()
    const params = useParams()
    const projectId = Number(params.id)
    const queryClient = useQueryClient()
    const [isModalOpen, setIsModalOpen] = React.useState(false)
    const onSuccess = () => {
        queryClient.invalidateQueries({ queryKey: ['features'] })
        invalidateActivityLogs()
    }

    const onAfterProjectDelete = () => {
        router.push('/app/projects')
        toast.success('Project deleted successfully')
    }

    // Create an array of tab items with optional count badges
    const tabItems: TabItem[] = [
        {
            id: 'details',
            label: 'Details',
            icon: <CircleAlert className="h-4 w-4" />,
            content: <ProjectDetailsTab project={project} refetchProject={refetchProject} />,
            count: 5,
        },
        {
            id: 'features',
            label: 'Features',
            content: <FeaturesTab />,
            count: 12,
            icon: <Puzzle className="h-4 w-4" />,
        },

        {
            id: 'activityLog',
            label: 'Activity Log',
            icon: <Logs className="h-4 w-4" />,
            content: <ActivityLog />,
            count: 8,
        },
    ]

    return (
        <div className="flex rounded-lg bg-transparent py-4 items-center">
            <Tabs value={projectActiveTab} onValueChange={setProjectActiveTab} className="w-full">
                <div className="flex flex-row justify-between items-center w-full border-b pb-2">
                    <TabsList className="grid grid-cols-3 h-full ">
                        {tabItems.map((tab) => (
                            <TabsTrigger
                                key={tab.id}
                                value={tab.id}
                                className="cursor-pointer flex items-center gap-2 py-2 data-[state=inactive]:text-[#575757] data-[state=active]:bg-[#FFFFFF] data-[state=active]:shadow-none">
                                {tab?.icon}
                                <span>{tab.label}</span>
                            </TabsTrigger>
                        ))}
                    </TabsList>
                    <div className="flex gap-4 items-center">
                        <DeleteWithAlert
                            title="Are you sure you want to delete this project?"
                            description="This action cannot be undo."
                            endpoint={`${endpoints.project.deleteProject.replace(':id', projectId.toString())}`}
                            onAfterSuccess={onAfterProjectDelete}
                            isAlertOpen={isModalOpen}
                            setIsAlertOpen={setIsModalOpen}>
                            <Trash2 size={14} color="#9B9B9B" />
                        </DeleteWithAlert>
                        <CreateFeatureModal projectId={projectId} onSuccess={onSuccess}>
                            <Button>
                                {' '}
                                <PlusCircle className="h-4 w-4" /> Add Feature
                            </Button>
                        </CreateFeatureModal>
                    </div>
                </div>
                {tabItems.map((tab) => (
                    <TabsContent key={tab.id} value={tab.id} className="px-2 ">
                        {tab.content}
                    </TabsContent>
                ))}
            </Tabs>
        </div>
    )
}
