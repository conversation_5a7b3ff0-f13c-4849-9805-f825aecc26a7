// hooks/useNotifications.ts
import { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { AxiosError } from 'axios'

// Constants
const SSE_RECONNECT_DELAY = 5000
const MAX_RECONNECT_ATTEMPTS = 3

// Types
export interface NotificationActor {
    first_name: string
    last_name: string
    img_url?: string
}

export interface Notification {
    id: number
    message: string
    isRead: boolean
    timeAgo: string
    actor: NotificationActor
    createdAt?: string
}

export interface UseNotificationsProps {
    userId?: number
    workspaceId?: number
}

export interface UseNotificationsReturn {
    notifications: Notification[]
    unreadNotifications: Notification[]
    displayedNotifications: Notification[]
    unreadCount: number
    isUnreadTab: boolean
    setIsUnreadTab: (isUnread: boolean) => void
    markAsRead: {
        mutate: (id: number) => void
        isPending: boolean
    }
    markAllAsRead: {
        mutate: () => void
        isPending: boolean
    }
    isLoading: boolean
    error: Error | null
    refetch: () => void
}

// Environment validation
const getSSEUrl = () => {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL
    if (!baseUrl) {
        console.error('NEXT_PUBLIC_API_URL is not configured')
        return null
    }
    return baseUrl
}

// Custom hook for notifications
export const useNotifications = ({ userId, workspaceId }: UseNotificationsProps): UseNotificationsReturn => {
    const queryClient = useQueryClient()
    const [isUnreadTab, setIsUnreadTab] = useState(true)
    const [notifications, setNotifications] = useState<Notification[]>([])
    const sseRef = useRef<EventSource | null>(null)
    const reconnectAttemptsRef = useRef(0)
    const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)

    // Query key factory
    const getQueryKey = useCallback(
        (tab: boolean) => ['notifications', userId, workspaceId, tab ? 'unread' : 'all'],
        [userId, workspaceId],
    )

    // Fetch notifications
    const { data, refetch, isLoading, error } = useQuery({
        queryKey: getQueryKey(isUnreadTab),
        queryFn: async () => {
            const res = await api.get(endpoints.notification.listNotifications, {
                params: { workspaceId, is_unread: isUnreadTab },
            })
            return res.data.data as Notification[]
        },
        enabled: !!userId && !!workspaceId,
        retry: 3,
    })

    // Update local state when data changes
    useEffect(() => {
        if (data) {
            setNotifications(data)
        }
    }, [data])

    // SSE connection with reconnection logic
    const connectSSE = useCallback(() => {
        if (!userId || !workspaceId) return

        const baseUrl = getSSEUrl()
        if (!baseUrl) return

        // Close existing connection
        if (sseRef.current) {
            sseRef.current.close()
        }

        const eventSource = new EventSource(`${baseUrl}/notification/events?userId=${userId}&workspaceId=${workspaceId}`)

        eventSource.onopen = () => {
            console.log('SSE connection opened')
            reconnectAttemptsRef.current = 0 // Reset on successful connection
        }

        eventSource.onmessage = (event) => {
            try {
                const newNotification: Notification = JSON.parse(event.data)

                refetch()

                // Update local state if on unread tab
                if (isUnreadTab) {
                    setNotifications((prev) => [newNotification, ...prev])
                }
            } catch (err) {
                console.error('Failed to parse SSE message', err)
            }
        }

        eventSource.onerror = (err) => {
            console.log('SSE error:', err)

            if (eventSource.readyState === EventSource.CLOSED) {
                // Attempt reconnection with exponential backoff
                if (reconnectAttemptsRef.current < MAX_RECONNECT_ATTEMPTS) {
                    const delay = SSE_RECONNECT_DELAY * Math.pow(2, reconnectAttemptsRef.current)
                    reconnectAttemptsRef.current += 1

                    reconnectTimeoutRef.current = setTimeout(() => {
                        console.log(`Attempting SSE reconnection ${reconnectAttemptsRef.current}/${MAX_RECONNECT_ATTEMPTS}`)
                        connectSSE()
                    }, delay)
                } else {
                    console.log('Max SSE reconnection attempts reached')
                }
            }
        }

        sseRef.current = eventSource
    }, [userId, workspaceId, isUnreadTab, refetch])

    // Initialize SSE connection
    useEffect(() => {
        connectSSE()

        return () => {
            if (sseRef.current) {
                sseRef.current.close()
                sseRef.current = null
            }
            if (reconnectTimeoutRef.current) {
                clearTimeout(reconnectTimeoutRef.current)
            }
        }
    }, [connectSSE])

    // Mark one notification as read
    const markAsRead = useMutation({
        mutationFn: async (id: number) => {
            const url = endpoints.notification.markAsRead.replace(':id', id.toString())
            return await api.patch(url)
        },
        onMutate: async (id: number) => {
            // Cancel outgoing refetches
            await queryClient.cancelQueries({ queryKey: getQueryKey(isUnreadTab) })

            // Snapshot previous value
            const previousNotifications = queryClient.getQueryData(getQueryKey(isUnreadTab))

            // Optimistically update cache
            queryClient.setQueryData(
                getQueryKey(isUnreadTab),
                (old: Notification[] | undefined) => old?.map((n) => (n.id === id ? { ...n, isRead: true } : n)) || [],
            )

            // Update local state
            setNotifications((prev) => prev.map((n) => (n.id === id ? { ...n, isRead: true } : n)))

            return { previousNotifications, id }
        },
        onError: (error: AxiosError, id: number, context) => {
            console.error('Failed to mark notification as read', error)

            // Rollback optimistic update
            if (context?.previousNotifications) {
                queryClient.setQueryData(getQueryKey(isUnreadTab), context.previousNotifications)
                setNotifications(context.previousNotifications as Notification[])
            }
        },
        onSettled: () => {
            // Invalidate and refetch
            queryClient.invalidateQueries({ queryKey: getQueryKey(isUnreadTab) })
        },
    })

    // Mark all as read
    const markAllAsRead = useMutation({
        mutationFn: async () => {
            return await api.post(endpoints.notification.markAsAllRead, {
                workspaceId: workspaceId,
            })
        },
        onMutate: async () => {
            await queryClient.cancelQueries({ queryKey: getQueryKey(isUnreadTab) })

            const previousNotifications = queryClient.getQueryData(getQueryKey(isUnreadTab))

            // Mark all as read in cache
            queryClient.setQueryData(
                getQueryKey(isUnreadTab),
                (old: Notification[] | undefined) => old?.map((n) => ({ ...n, isRead: true })) || [],
            )

            // Clear unread notifications if on unread tab
            if (isUnreadTab) {
                setNotifications([])
            } else {
                setNotifications((prev) => prev.map((n) => ({ ...n, isRead: true })))
            }

            return { previousNotifications }
        },
        onError: (error: AxiosError, _, context) => {
            console.error('Failed to mark all as read', error)

            if (context?.previousNotifications) {
                queryClient.setQueryData(getQueryKey(isUnreadTab), context.previousNotifications)
                setNotifications(context.previousNotifications as Notification[])
            }
        },
        onSettled: () => {
            queryClient.invalidateQueries({ queryKey: getQueryKey(isUnreadTab) })
        },
    })

    // Computed values
    const unreadNotifications = useMemo(() => notifications.filter((n) => !n.isRead), [notifications])

    const displayedNotifications = useMemo(
        () => (isUnreadTab ? unreadNotifications : notifications),
        [isUnreadTab, unreadNotifications, notifications],
    )

    const unreadCount = unreadNotifications.length

    return {
        notifications,
        unreadNotifications,
        displayedNotifications,
        unreadCount,
        isUnreadTab,
        setIsUnreadTab,
        markAsRead,
        markAllAsRead,
        isLoading,
        error,
        refetch,
    }
}
