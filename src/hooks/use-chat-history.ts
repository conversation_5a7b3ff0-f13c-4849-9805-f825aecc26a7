import { useState, useEffect } from 'react'
import type { Message } from '@/types/project'
import { api } from '@/config/axios-config'
import { MessagePart } from '@/types/project'
import { useAuthStore } from '@/store/auth.store'

interface ChatMessage {
    sender: 'user' | 'ai'
    content: string
    parts: MessagePart[]
}

export const useChatHistory = (userId: string) => {
    const [sessions, setSessions] = useState<string[]>([])
    const [history, setHistory] = useState<Message[]>([])
    const workspaceId = useAuthStore((state) => state.currentWorkspace?.id)

    useEffect(() => {
        if (!userId || !workspaceId) return

        api.get(`/sessions`, { params: { userId, workspaceId } })
            .then((res) => setSessions(res.data.sessions))
            .catch(console.error)
    }, [userId, workspaceId])

    const loadHistory = async (sessionId: string): Promise<Message[]> => {
        try {
            const res = await api.get(`/sessions/${sessionId}/history`)
            const chatMessages = res.data.history as ChatMessage[]
            const transformedMessages: Message[] = chatMessages.map((msg, index) => ({
                id: `${sessionId}-${index}`,
                content: msg.content,
                sender: msg.sender,
                parts: msg.parts,
                timestamp: new Date(),
                expanded: false,
            }))
            setHistory(transformedMessages)
            return transformedMessages
        } catch (err) {
            console.error(err)
            return []
        }
    }

    return { sessions, history, loadHistory }
}
