'use client'
import { usePathname } from 'next/navigation'
import { useState, useEffect } from 'react'

export function useAuth() {
    const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false)
    const [isLoading, setIsLoading] = useState<boolean>(true)
    const pathname = usePathname()

    useEffect(() => {
        const getAuthSession = () => {
            if (typeof document !== 'undefined') {
                const cookies = document.cookie.split('; ').reduce((acc: Record<string, string>, cookie) => {
                    const [name, value] = cookie.split('=')
                    acc[name] = value
                    return acc
                }, {})

                return cookies['auth_session']
            }
            return null
        }

        const authCookie = getAuthSession()
        setIsAuthenticated(!!authCookie)
        setIsLoading(false)
    }, [pathname])

    return { isAuthenticated, isLoading }
}
