import { SSEResponse } from '@/types/project'

export class SSEService {
    private decoder = new TextDecoder()

    processEventStreamChunk(chunk: string): string {
        let newContent = ''

        try {
            const lines = chunk.split('\n')

            for (const line of lines) {
                if (line.startsWith('data:')) {
                    const dataContent = line.substring(5).trim()

                    try {
                        const parsedData: SSEResponse = JSON.parse(dataContent)
                        if (parsedData.content !== undefined) {
                            newContent += parsedData.content
                        }
                    } catch (jsonError) {
                        console.error('Error parsing JSON:', jsonError)
                    }
                }
            }
        } catch (error) {
            console.error('Error processing chunk:', error)
        }

        return newContent
    }

    async processStream<T>(response: Response, onData: (data: T) => void, onContent?: (content: string) => void): Promise<void> {
        if (!response.body) {
            throw new Error('No response body from the server')
        }

        const reader = response.body.getReader()
        let buffer = ''

        try {
            while (true) {
                const { done, value } = await reader.read()
                if (done) break

                const decodedChunk = this.decoder.decode(value, { stream: true })
                buffer += decodedChunk

                const lines = buffer.split('\n')
                buffer = lines.pop() || ''

                for (const line of lines) {
                    if (line.startsWith('data:')) {
                        try {
                            const dataContent = line.substring(5).trim()
                            const parsedData: SSEResponse = JSON.parse(dataContent)

                            if (parsedData.content && onContent) {
                                onContent(parsedData.content)
                            }

                            onData(parsedData as T)
                        } catch (jsonError) {
                            console.error('Error parsing SSE data:', jsonError)
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock()
        }
    }
}
