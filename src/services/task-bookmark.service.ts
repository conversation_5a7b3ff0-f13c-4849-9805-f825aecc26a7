import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { toast } from 'sonner'
import { invalidateTasks } from './invalidate-query.service'

export const bookmarkTask = async (taskId: number) => {
    try {
        const url = endpoints.tasks.bookmarkTask.replace(':id', taskId.toString())
        const response = await api.patch(url)
        invalidateTasks()
        toast.success(response.data.message)
    } catch (error: unknown) {
        if (error instanceof Error) {
            toast.error(error.message || 'Failed to bookmark task')
        } else {
            toast.error('Failed to bookmark task')
        }
    }
}
