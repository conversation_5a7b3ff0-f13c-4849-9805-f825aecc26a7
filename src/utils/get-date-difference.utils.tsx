type TaskDueInfo = {
    label: string
    daysLeft: number
}

export function getDateDifference(taskDueDate: string | Date | null | undefined, today: Date = new Date()): TaskDueInfo | null {
    if (!taskDueDate) return null

    const dueDate = new Date(taskDueDate)

    // Reset hours for date-only comparison
    today.setHours(0, 0, 0, 0)
    dueDate.setHours(0, 0, 0, 0)

    const diffInTime = dueDate.getTime() - today.getTime()
    const diffInDays = Math.round(diffInTime / (1000 * 60 * 60 * 24)) // Ensure whole number

    let label = ''

    switch (diffInDays) {
        case 0:
            label = 'Today'
            break
        case 1:
            label = 'Tomorrow'
            break
        case 2:
            label = 'Day after tomorrow'
            break
        default:
            return null
    }

    return {
        label,
        daysLeft: diffInDays,
    }
}
