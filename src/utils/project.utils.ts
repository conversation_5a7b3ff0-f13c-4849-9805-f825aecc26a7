import { Message } from '@/types/project'

export const extractProjectName = (messages: Message[]): string => {
    const aiMessages = messages.filter((msg) => msg.sender === 'ai')
    const projectNameRegex = /Feature\/Project:\s*([^,\n]+)/i

    const projectMatch = aiMessages
        .slice()
        .reverse()
        .find((msg) => projectNameRegex.test(msg.content))

    if (projectMatch) {
        const nameMatch = projectMatch.content.match(projectNameRegex)
        if (nameMatch?.[1]) {
            return nameMatch[1].trim()
        }
    }

    return ''
}

export const extractTechStack = (messages: Message[]): Record<string, string> => {
    const aiMessages = messages.filter((msg) => msg.sender === 'ai')
    const techStackRegex = /Tech Stack:\s*([^\n]+)/i

    const techStackMatch = aiMessages
        .slice()
        .reverse()
        .find((msg) => techStackRegex.test(msg.content))

    if (techStackMatch) {
        const stackMatch = techStackMatch.content.match(techStackRegex)
        if (stackMatch?.[1]) {
            const techStackText = stackMatch[1].trim()
            const techList = techStackText.split(',').map((tech) => tech.trim())

            return techList.reduce(
                (acc, tech) => {
                    acc[tech] = tech
                    return acc
                },
                {} as Record<string, string>,
            )
        }
    }

    return {}
}
