import type React from 'react'
export function FigmaIcon(props: React.SVGProps<SVGSVGElement>) {
    return (
        <svg viewBox="0 0 24 24" fill="none" width={7} height={7} xmlns="http://www.w3.org/2000/svg" {...props}>
            <path
                d="M8 24C10.2091 24 12 22.2091 12 20V16H8C5.79086 16 4 17.7909 4 20C4 22.2091 5.79086 24 8 24Z"
                fill="#0ACF83"
            />
            <path d="M4 12C4 9.79086 5.79086 8 8 8H12V16H8C5.79086 16 4 14.2091 4 12Z" fill="#A259FF" />
            <path d="M4 4C4 1.79086 5.79086 0 8 0H12V8H8C5.79086 8 4 6.20914 4 4Z" fill="#F24E1E" />
            <path d="M12 0H16C18.2091 0 20 1.79086 20 4C20 6.20914 18.2091 8 16 8H12V0Z" fill="#FF7262" />
            <path
                d="M20 12C20 14.2091 18.2091 16 16 16C13.7909 16 12 14.2091 12 12C12 9.79086 13.7909 8 16 8C18.2091 8 20 9.79086 20 12Z"
                fill="#1ABCFE"
            />
        </svg>
    )
}

export function CloudIcon(props: React.SVGProps<SVGSVGElement>) {
    return (
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <path
                d="M13.5 22.5H3C2.60218 22.5 2.22064 22.342 1.93934 22.0607C1.65804 21.7794 1.5 21.3978 1.5 21V3C1.5 2.60218 1.65804 2.22064 1.93934 1.93934C2.22064 1.65804 2.60218 1.5 3 1.5H21C21.3978 1.5 21.7794 1.65804 22.0607 1.93934C22.342 2.22064 22.5 2.60218 22.5 3V13.5"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path d="M16.5 19.5H16.515" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path
                d="M12 19.5L13.5 21L16.5 18"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M22.5 16.5C22.5 17.6935 22.0259 18.8381 21.182 19.682C20.3381 20.5259 19.1935 21 18 21C16.8065 21 15.6619 20.5259 14.818 19.682C13.9741 18.8381 13.5 17.6935 13.5 16.5C13.5 15.3065 13.9741 14.1619 14.818 13.318C15.6619 12.4741 16.8065 12 18 12C19.1935 12 20.3381 12.4741 21.182 13.318C22.0259 14.1619 22.5 15.3065 22.5 16.5Z"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </svg>
    )
}

export function PythonIcon(props: React.SVGProps<SVGSVGElement>) {
    return (
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <path
                d="M11.751 2.25C6.0765 2.25 6.44775 4.3785 6.44775 4.3785L6.4515 6.6615H11.8357V7.5H4.1805C4.1805 7.5 1.5 7.10175 1.5 12.8528C1.5 18.6045 3.8625 18.3892 3.8625 18.3892H5.7V16.0207C5.7 16.0207 5.61525 13.6582 8.0625 13.6582H13.4055C13.4055 13.6582 15.7035 13.6973 15.7035 11.4465V4.9515C15.7035 4.9515 16.1407 2.25 11.751 2.25ZM8.5005 3.7485C8.5005 3.7485 9.5175 2.7315 11.7517 2.7315C13.986 2.7315 14.9985 3.7485 14.9985 3.7485C14.9985 3.7485 16.0155 4.7655 16.0155 6.999C16.0155 9.2325 14.9985 10.2495 14.9985 10.2495C14.9985 10.2495 13.9815 11.2665 11.7472 11.2665C9.513 11.2665 8.5005 10.2495 8.5005 10.2495C8.5005 10.2495 7.4835 9.2325 7.4835 6.999C7.4835 4.7655 8.5005 3.7485 8.5005 3.7485Z"
                fill="#3776AB"
            />
            <path
                d="M12.249 21.75C17.9235 21.75 17.5522 19.6215 17.5522 19.6215L17.5485 17.3385H12.1642V16.5H19.8195C19.8195 16.5 22.5 16.8982 22.5 11.1472C22.5 5.3955 20.1375 5.61075 20.1375 5.61075H18.3V7.97925C18.3 7.97925 18.3847 10.3418 15.9375 10.3418H10.5945C10.5945 10.3418 8.2965 10.3027 8.2965 12.5535V19.0485C8.2965 19.0485 7.85925 21.75 12.249 21.75ZM15.4995 20.2515C15.4995 20.2515 14.4825 21.2685 12.2482 21.2685C10.014 21.2685 9.0015 20.2515 9.0015 20.2515C9.0015 20.2515 7.9845 19.2345 7.9845 17.001C7.9845 14.7675 9.0015 13.7505 9.0015 13.7505C9.0015 13.7505 10.0185 12.7335 12.2527 12.7335C14.487 12.7335 15.4995 13.7505 15.4995 13.7505C15.4995 13.7505 16.5165 14.7675 16.5165 17.001C16.5165 19.2345 15.4995 20.2515 15.4995 20.2515Z"
                fill="#FFD43B"
            />
        </svg>
    )
}

export function ReactIcon(props: React.SVGProps<SVGSVGElement>) {
    return (
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <path
                d="M12 13.5C12.8284 13.5 13.5 12.8284 13.5 12C13.5 11.1716 12.8284 10.5 12 10.5C11.1716 10.5 10.5 11.1716 10.5 12C10.5 12.8284 11.1716 13.5 12 13.5Z"
                fill="currentColor"
            />
            <path
                d="M12 22.5C11.3243 22.5 10.6367 22.0859 9.87132 21.2812C9.11718 20.4844 8.40429 19.3242 7.79882 17.9414C7.19335 16.5586 6.7461 15.0117 6.47304 13.4297C6.20703 11.8672 6.11132 10.3594 6.19921 9.01172C6.28124 7.67578 6.54726 6.55078 6.97499 5.73047C7.40273 4.91016 7.97343 4.5 8.59999 4.5C9.27577 4.5 9.96327 4.91406 10.7274 5.71875C11.4837 6.51562 12.1966 7.67578 12.802 9.05859C13.4075 10.4414 13.8547 11.9883 14.1278 13.5703C14.3938 15.1328 14.4895 16.6406 14.4016 17.9883C14.3196 19.3242 14.0536 20.4492 13.6258 21.2695C13.1981 22.0898 12.6274 22.5 12 22.5ZM8.59999 6C8.42577 6 8.17734 6.1875 7.91132 6.75C7.64531 7.3125 7.43905 8.17969 7.37343 9.28125C7.30781 10.3828 7.39648 11.7188 7.63788 13.1484C7.87929 14.5781 8.28163 16.0078 8.83788 17.2969C9.39413 18.5859 10.0348 19.6406 10.6872 20.3438C11.3395 21.0469 11.9102 21.3281 12.2098 21.0117C12.3841 20.8242 12.6325 20.625 12.8985 20.0625C13.1645 19.5 13.3708 18.6328 13.4364 17.5312C13.502 16.4297 13.4133 15.0938 13.1719 13.6641C12.9305 12.2344 12.5282 10.8047 11.9719 9.51562C11.4157 8.22656 10.775 7.17188 10.1227 6.46875C9.47038 5.76562 8.89968 6 8.59999 6Z"
                fill="currentColor"
            />
            <path
                d="M15.4008 22.5C14.7727 22.5 14.202 22.0898 13.7742 21.2695C13.3465 20.4492 13.0805 19.3242 12.9984 17.9883C12.9105 16.6406 13.0062 15.1328 13.2723 13.5703C13.5453 11.9883 13.9926 10.4414 14.598 9.05859C15.2035 7.67578 15.9164 6.51562 16.6727 5.71875C17.4367 4.91406 18.1242 4.5 18.8 4.5C19.4266 4.5 19.9973 4.91016 20.425 5.73047C20.8527 6.55078 21.1188 7.67578 21.2008 9.01172C21.2887 10.3594 21.193 11.8672 20.927 13.4297C20.6539 15.0117 20.2066 16.5586 19.6012 17.9414C18.9957 19.3242 18.2828 20.4844 17.5287 21.2812C16.7633 22.0859 16.0758 22.5 15.4008 22.5ZM18.8 6C18.5004 6 17.9297 5.76562 17.2773 6.46875C16.625 7.17188 15.9844 8.22656 15.4281 9.51562C14.8719 10.8047 14.4695 12.2344 14.2281 13.6641C13.9867 15.0938 13.898 16.4297 13.9637 17.5312C14.0293 18.6328 14.2355 19.5 14.5016 20.0625C14.7676 20.625 15.016 20.8242 15.1902 21.0117C15.4898 21.3281 16.0605 21.0469 16.7129 20.3438C17.3652 19.6406 18.0059 18.5859 18.5621 17.2969C19.1184 16.0078 19.5207 14.5781 19.7621 13.1484C20.0035 11.7188 20.0922 10.3828 20.0266 9.28125C19.9609 8.17969 19.7547 7.3125 19.4887 6.75C19.2227 6.1875 18.9742 6 18.8 6Z"
                fill="currentColor"
            />
        </svg>
    )
}

export function TypeScriptIcon(props: React.SVGProps<SVGSVGElement>) {
    return (
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <path d="M3 3H21V21H3V3Z" fill="currentColor" />
            <path d="M14 18V15.25H11.5V14H14V11.25H10V10H15.5V18H14Z" fill="white" />
            <path d="M8.5 18V16.75H10V10H8.5V8.75H13V10H11.5V16.75H13V18H8.5Z" fill="white" />
        </svg>
    )
}

export function JavaScriptIcon(props: React.SVGProps<SVGSVGElement>) {
    return (
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <path d="M3 3H21V21H3V3Z" fill="currentColor" />
            <path
                d="M12 17.25C12.8333 17.25 13.5 17 14 16.5L15 17.75C14.1667 18.5833 13.1667 19 12 19C10.1667 19 8.75 17.5833 8.75 15.25C8.75 13 10.1667 11.5 12 11.5C13.1667 11.5 14.1667 11.9167 15 12.75L14 14C13.5 13.5 12.8333 13.25 12 13.25C11.0833 13.25 10.5 14 10.5 15.25C10.5 16.5 11.0833 17.25 12 17.25Z"
                fill="white"
            />
            <path d="M16 19V11.5H17.75V19H16Z" fill="white" />
        </svg>
    )
}

export function NodeIcon(props: React.SVGProps<SVGSVGElement>) {
    return (
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <path d="M12 1.5L3 6.75V17.25L12 22.5L21 17.25V6.75L12 1.5Z" fill="currentColor" />
            <path
                d="M12 5.25V18.75M12 5.25L7.5 7.875M12 5.25L16.5 7.875M12 18.75L7.5 16.125M12 18.75L16.5 16.125M7.5 7.875V13.125L12 15.75L16.5 13.125V7.875M7.5 7.875L12 10.5L16.5 7.875"
                stroke="white"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </svg>
    )
}
