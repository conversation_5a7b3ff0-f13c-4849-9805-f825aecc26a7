'use client'

import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'

export async function uploadFile(formData: FormData) {
    try {
        const file = formData.get('file') as File

        if (!file) return { success: false, error: 'No file provided' }

        // Get presigned URL from backend
        const { data } = await api.get(endpoints.uploadFile.getPresignedUrl, {
            params: { fileName: file.name },
        })

        if (!data.url || !data.publicUrl) {
            return { success: false, error: data.error || 'Upload failed' }
        }

        const { url: uploadUrl, publicUrl: fileUrl } = data

        // Upload file to MinIO
        const uploadResponse = await fetch(uploadUrl, {
            method: 'PUT',
            headers: { 'Content-Type': file.type },
            body: file,
        })

        if (!uploadResponse.ok) {
            return { success: false, error: `Upload failed with status ${uploadResponse.status}` }
        }

        return { success: true, fileUrl }
    } catch (error: unknown) {
        console.error(error)
        const errorMessage = error instanceof Error ? error.message : 'Upload failed'
        return { success: false, error: errorMessage }
    }
}
