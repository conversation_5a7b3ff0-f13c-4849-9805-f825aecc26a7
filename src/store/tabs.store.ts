import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'

type TabStore = {
    //for chat and history
    activeTab: string
    setActiveTab: (tab: string) => void
    //for kanban tabs
    viewMode: string
    setViewMode: (tab: string) => void
    //for project tabs
    projectActiveTab: string
    setProjectActiveTab: (tab: string) => void
    //for task details page bottom tab
    taskActiveTab: string
    setTaskActiveTab: (tab: string) => void

    //for settings page
    settingsActiveTab: string
    setSettingsActiveTab: (tab: string) => void
    //for team management page
    teamManagementActiveTab: string
    setTeamManagementActiveTab: (tab: string) => void
}

export const useTabStore = create<TabStore>()(
    persist(
        (set) => ({
            activeTab: 'chat', // default tab
            setActiveTab: (tab) => set({ activeTab: tab }),
            //for kan ban
            viewMode: 'kanban',
            setViewMode: (tab) => set({ viewMode: tab }),
            //for project tabs
            projectActiveTab: 'details',
            setProjectActiveTab: (tab) => set({ projectActiveTab: tab }),
            //for task details page bottom tab
            taskActiveTab: 'comments',
            setTaskActiveTab: (tab) => set({ taskActiveTab: tab }),
            //for settings page
            settingsActiveTab: 'profile',
            setSettingsActiveTab: (tab) => set({ settingsActiveTab: tab }),
            //for team management page
            teamManagementActiveTab: 'overview',
            setTeamManagementActiveTab: (tab) => set({ teamManagementActiveTab: tab }),
        }),
        {
            name: 'tab-storage',
            storage: createJSONStorage(() => sessionStorage),
            partialize: (state) => ({
                viewMode: state.viewMode,
            }),
        },
    ),
)
