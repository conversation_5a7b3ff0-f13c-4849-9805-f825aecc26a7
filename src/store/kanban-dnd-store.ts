import { create } from 'zustand'
import debounce from 'lodash/debounce'
import type { DnDColumnType, DnDTaskType, TaskUpdateApiBodyType } from '../components/kanban-dnd/types'
import { arrayMove } from '@dnd-kit/sortable'

interface KanbanState {
    columns: DnDColumnType[]
    tasks: DnDTaskType[]
    taskUpdateApiBody: TaskUpdateApiBodyType | null

    // Column actions
    setColumns: (columns: DnDColumnType[]) => void
    addColumn: (column: DnDColumnType) => void
    updateColumn: (id: number, data: Partial<DnDColumnType>) => void
    deleteColumn: (id: number) => void
    reorderColumn: (oldIndex: number, newIndex: number) => void
    setTaskUpdateApiBody: (task: TaskUpdateApiBodyType | null) => void

    // Task actions
    setTasks: (tasks: DnDTaskType[]) => void
    addTask: (task: DnDTaskType) => void
    updateTask: (id: number, data: Partial<DnDTaskType>) => void
    deleteTask: (id: number) => void
    reorderTask: (columnId: number, oldIndex: number, newIndex: number) => void
    moveTaskToColumn: (taskId: number, sourceColumnId: number, destinationColumnId: number, index?: number) => void
}

export const useKanbanStore = create<KanbanState>((set, get) => {
    // Create a debounced version of setTaskUpdateApiBody
    const debouncedSetTaskUpdateApiBody = debounce((task: TaskUpdateApiBodyType | null) => {
        set({ taskUpdateApiBody: task })
    }, 100)

    return {
        columns: [], // Your initial columns here
        tasks: [], // Your initial tasks here
        taskUpdateApiBody: null, // Your initial task update API body here
        // Column actions
        setColumns: (columns) => set({ columns }),
        setTaskUpdateApiBody: (task) => set({ taskUpdateApiBody: task }),

        addColumn: (column) =>
            set((state) => ({
                columns: [...state.columns, column],
            })),

        updateColumn: (id, data) =>
            set((state) => ({
                columns: state.columns.map((column) => (column.id === id ? { ...column, ...data } : column)),
            })),

        deleteColumn: (id) =>
            set((state) => ({
                columns: state.columns.filter((column) => column.id !== id),
                // Also remove tasks in this column
                tasks: state.tasks.filter((task) => task.status_id !== id),
            })),

        reorderColumn: (oldIndex, newIndex) => {
            const state = get()
            const reorderedColumns = arrayMove(state.columns, oldIndex, newIndex).map((column, index) => ({
                ...column,
                column_order: index + 1,
            }))

            set({
                columns: reorderedColumns,
            })
        },

        // Task actions
        setTasks: (tasks) => set({ tasks }),

        addTask: (task) =>
            set((state) => ({
                tasks: [...state.tasks, task],
            })),

        updateTask: (id, data) =>
            set((state) => ({
                tasks: state.tasks.map((task) =>
                    task.id === id
                        ? {
                              ...task,
                              ...data,
                              // If status_id is updated, also update taskStatus
                              ...(data.status_id
                                  ? {
                                        taskStatus: state.columns.find((col) => col.id === data.status_id) || task.taskStatus,
                                    }
                                  : {}),
                          }
                        : task,
                ),
            })),

        deleteTask: (id) =>
            set((state) => ({
                tasks: state.tasks.filter((task) => task.id !== id),
            })),

        reorderTask: (columnId, oldIndex, newIndex) => {
            const state = get()
            const tasksInColumn = state.tasks.filter((task) => task.status_id === columnId)
            const movedTask = tasksInColumn[oldIndex]

            const reorderedTasks = arrayMove(tasksInColumn, oldIndex, newIndex)
            const otherTasks = state.tasks.filter((task) => task.status_id !== columnId)
            const updatedTasks = [...otherTasks, ...reorderedTasks]
            const movedTaskIndex = newIndex

            // Set tasks immediately
            set({ tasks: updatedTasks })

            // Debounce the taskUpdateApiBody update
            debouncedSetTaskUpdateApiBody({
                ...movedTask,
                status: columnId,
                movedToIndex: movedTaskIndex,
            })
        },

        moveTaskToColumn: (taskId: number, sourceColumnId: number, destinationColumnId: number, index?: number) => {
            const state = get()

            // Get the task to move
            const taskToMove = state.tasks.find((task) => task.id === taskId)
            if (!taskToMove) return

            // Get the source and destination columns
            const destinationColumn = state.columns.find((col) => col.id === destinationColumnId)
            if (!destinationColumn) return

            // Remove the task from its current position
            const tasksWithoutMoved = state.tasks.filter((task) => task.id !== taskId)

            // Create the updated task with new column info
            const updatedTask = {
                ...taskToMove,
                status_id: destinationColumnId,
                taskStatus: {
                    id: destinationColumn.id,
                    short_code: destinationColumn.short_code,
                    label: destinationColumn.label,
                    colour: destinationColumn.colour,
                },
            }

            let updatedTasks: DnDTaskType[] = []
            let tasksInDestination: DnDTaskType[] = []

            // If no specific index is provided, add to the end of the destination column
            if (index === undefined) {
                updatedTasks = [...tasksWithoutMoved, updatedTask]
                tasksInDestination = tasksWithoutMoved.filter((task) => task.status_id === destinationColumnId)
                index = [...tasksInDestination, taskToMove].length - 1
            } else {
                // Get tasks in the destination column
                tasksInDestination = tasksWithoutMoved.filter((task) => task.status_id === destinationColumnId)

                // Get tasks not in the destination column
                const tasksNotInDestination = tasksWithoutMoved.filter((task) => task.status_id !== destinationColumnId)

                // Insert the task at the specified index
                tasksInDestination.splice(index, 0, updatedTask)

                updatedTasks = [...tasksNotInDestination, ...tasksInDestination]
            }

            const movedTaskIndex = index

            // Set tasks immediately
            set({ tasks: updatedTasks })

            // Debounce the taskUpdateApiBody update
            debouncedSetTaskUpdateApiBody({
                ...taskToMove,
                status: destinationColumnId,
                movedToIndex: movedTaskIndex,
            })
        },
    }
})
