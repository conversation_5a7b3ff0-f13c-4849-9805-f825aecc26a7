'use client'

import { useState } from 'react'
import { FormItem, FormLabel, FormControl } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import AuthFormErrorMsg from '../auth-form-error-msg'
import { FieldValues } from 'react-hook-form'
import { Eye, EyeOff } from 'lucide-react'
import { Button } from '../ui/button'

type PasswordInputFieldProps<TFieldValues extends FieldValues> = {
    label: string
    placeholder?: string
    field: TFieldValues
    itemClassName?: string
    labelClassName?: string
    inputClassName?: string
    showEyeIcon?: boolean
}

export default function PasswordInputField<TFieldValues extends FieldValues>({
    label,
    placeholder = 'Your password',
    field,
    itemClassName = 'w-full',
    labelClassName = 'text-[14px] text-[#414651]',
    inputClassName = 'bg-white rounded-sm',
    showEyeIcon = true,
}: PasswordInputFieldProps<TFieldValues>) {
    const [showPassword, setShowPassword] = useState(false)

    const togglePasswordVisibility = () => {
        setShowPassword((prev) => !prev)
    }

    return (
        <FormItem className={itemClassName}>
            <FormLabel className={labelClassName}>{label}</FormLabel>
            <div className="relative">
                <FormControl>
                    <Input
                        {...field}
                        type={showPassword ? 'text' : 'password'}
                        value={field.value || ''}
                        className={inputClassName}
                        placeholder={placeholder}
                    />
                </FormControl>
                {showEyeIcon && field.value && (
                    <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={togglePasswordVisibility}
                        className="absolute right-2 top-2.5 h-4 w-4 text-muted-foreground focus:outline-none">
                        {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </Button>
                )}
            </div>
            <AuthFormErrorMsg />
        </FormItem>
    )
}
