'use client'

import { useState, useEffect, useMemo, useCallback, memo } from 'react'
import { useQuery, useMutation } from '@tanstack/react-query'
import { Loader2 } from 'lucide-react'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import getStatusIcon from '@/components/get-status-icon'
import getStatusStyles from '@/utils/get-status-styles.utils'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import { AxiosError, AxiosResponse } from 'axios'

export interface Status {
    id: string | number
    label?: string
    color?: string
}

interface StatusSelectProps {
    entityId: string | number
    entityType: 'task' | 'feature' | 'project'
    initialStatusId?: number
    fetchEndpoint: string
    updateEndpoint?: string
    className?: string
    onStatusChange?: (newStatus: Status) => void
    disabled?: boolean
    entityDetails?: Record<string, unknown>
    status_key?: string
    fallbackPlaceholder?: string
}

function StatusSelectComponent({
    entityId,
    entityType,
    initialStatusId,
    fetchEndpoint,
    updateEndpoint,
    className,
    onStatusChange,
    disabled = false,
    entityDetails,
    status_key = 'status',
    fallbackPlaceholder = 'Status',
}: StatusSelectProps) {
    const [selectedStatusId, setSelectedStatusId] = useState<number | undefined>(initialStatusId)

    // Memoized query key
    const queryKey = useMemo(() => ['statuses', entityType, fetchEndpoint], [entityType, fetchEndpoint])

    // Fetch statuses
    const { data: statuses, isLoading: isLoadingStatuses } = useQuery({
        queryKey,
        queryFn: async () => {
            const response = await api.get(fetchEndpoint)
            return response.data.data as Status[]
        },
    })

    // Mutation function
    const mutationFn = useCallback(
        async (statusId: number) => {
            if (!updateEndpoint) return
            const payload = {
                [`${entityType}_id`]: entityId,
                [status_key]: statusId,
                ...entityDetails,
            }
            const response = await api.put(updateEndpoint, payload)
            return response.data
        },
        [updateEndpoint, entityId, entityType, status_key, entityDetails],
    )

    const { mutate: updateStatus, isPending: isUpdating } = useMutation({
        mutationFn,
        onSuccess: useCallback(
            (data: AxiosResponse) => {
                toast.success('Status updated successfully')
                if (onStatusChange) {
                    onStatusChange(data?.data)
                }
                setSelectedStatusId(data?.data?.[status_key] ?? selectedStatusId)
            },
            [onStatusChange, selectedStatusId, status_key],
        ),
        onError: useCallback((err: AxiosError) => {
            axiosErrorToast(err, 'Failed to update status')
        }, []),
    })

    // Sync initial value
    useEffect(() => {
        if (initialStatusId !== undefined) {
            setSelectedStatusId(initialStatusId)
        }
    }, [initialStatusId])

    // Find selected status
    const selectedStatus = useMemo(() => statuses?.find((status) => status.id === selectedStatusId), [statuses, selectedStatusId])

    // Handle value change
    const handleValueChange = useCallback(
        (value: string) => {
            const numericValue = Number(value)
            setSelectedStatusId(numericValue)

            if (updateEndpoint) {
                updateStatus(numericValue)
                return
            }

            if (onStatusChange) {
                onStatusChange({ id: numericValue })
            }
        },
        [updateEndpoint, updateStatus, onStatusChange],
    )

    // Memoized select value
    const selectValue = useMemo(() => selectedStatusId?.toString(), [selectedStatusId])

    // Memoized disabled state
    const isDisabled = useMemo(() => disabled || isUpdating, [disabled, isUpdating])

    // Memoized status items
    const statusItems = useMemo(
        () =>
            statuses?.map((status) => (
                <SelectItem key={status.id} value={String(status.id)} className="flex items-center gap-2">
                    <div className="flex items-center gap-2">
                        {status.label && getStatusIcon(status.label)}
                        {status.label && (
                            <span className={cn('text-xs font-medium', status.color || getStatusStyles(status.label)?.text)}>
                                {status.label}
                            </span>
                        )}
                    </div>
                </SelectItem>
            )),
        [statuses],
    )

    if (isLoadingStatuses) {
        return (
            <div
                className={cn(
                    'flex h-9 items-center justify-between rounded-md border border-input bg-transparent px-3 py-2 text-sm',
                    className,
                )}>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                <span>Loading...</span>
            </div>
        )
    }

    return (
        <Select value={selectValue} onValueChange={handleValueChange} disabled={isDisabled}>
            <div
                className={cn(
                    'w-full overflow-hidden flex items-center justify-center border',
                    className,
                    getStatusStyles(selectedStatus?.label || '')?.border,
                )}>
                <SelectTrigger className="border-none shadow-none gap-0 w-full pl-0 text-[13px]">
                    {!isUpdating ? (
                        <SelectValue placeholder={fallbackPlaceholder}>
                            {selectedStatus ? (
                                <div className="flex items-center gap-1">
                                    {getStatusIcon(selectedStatus.label || '', 12)}
                                    <span
                                        className={cn(
                                            'text-sm',
                                            selectedStatus.color || getStatusStyles(selectedStatus.label || '')?.text,
                                        )}>
                                        {selectedStatus.label}
                                    </span>
                                </div>
                            ) : (
                                <>{fallbackPlaceholder}</>
                            )}
                        </SelectValue>
                    ) : (
                        <div className="w-[80px] flex justify-center">
                            <Loader2 className="h-3 w-3 animate-spin ml-2" />
                        </div>
                    )}
                </SelectTrigger>
            </div>
            <SelectContent>{statusItems}</SelectContent>
        </Select>
    )
}

// Export memoized component
export const StatusSelect = memo(StatusSelectComponent)
