import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Loader } from 'lucide-react'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Form } from './ui/form'
import { InviteEmailFields } from './form-fields'
import { findDuplicates, hasEmptyEmail } from '@/utils/email-utils'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'

import { z } from 'zod'
import { Separator } from './ui/separator'
import { Input } from './ui/input'
import { toast } from 'sonner'

const schema = z.object({
    emails: z
        .array(
            z.object({
                value: z.string().min(1, 'Email is required').email('Invalid email address'),
            }),
        )
        .min(1, 'At least one email is required'),
})

type FormSchema = z.infer<typeof schema>

interface TeamInviteModalProps {
    defaultLink?: string
    onInvite?: (emails: string[], link: string) => void
    isOpen?: boolean
    onOpenChange?: (state: boolean) => void
    showTrigger?: boolean
    isLoading?: boolean
}

export const TeamInviteModal: React.FC<TeamInviteModalProps> = ({
    defaultLink = 'test',
    onInvite,
    isOpen,
    onOpenChange,
    showTrigger = true,
    isLoading = false,
}) => {
    const [linkCopied, setLinkCopied] = useState(false)
    const SHAREABLE_LINK = defaultLink
    const copyLink = () => {
        navigator.clipboard.writeText(SHAREABLE_LINK)
        setLinkCopied(true)
        setTimeout(() => setLinkCopied(false), 3000)
        toast.success('Copied to clipboard!')
    }

    const form = useForm<FormSchema>({
        resolver: zodResolver(schema),
        defaultValues: {
            emails: [{ value: '' }],
        },
    })
    useEffect(() => {
        if (isOpen) {
            form.reset()
        }
    }, [isOpen, form])

    const { watch, handleSubmit } = form

    // Watch emails input and check for duplicates or empty fields
    const emails = watch('emails')
    const duplicateEmail = findDuplicates(emails) || ''
    const hasEmptyEmailField = hasEmptyEmail(emails)

    // Handle form submission
    const onFormSubmit = (data: FormSchema) => {
        const validEmails = data.emails.map((email) => email.value.trim())
        // Trigger the onInvite callback if provided
        onInvite?.(validEmails, defaultLink)
    }
    const clearStatesOnClose = () => {
        onOpenChange?.(false)
        form.reset()
    }

    return (
        <>
            {showTrigger && (
                <div
                    onClick={() => onOpenChange?.(true)}
                    className="cursor-pointer"
                    role="button"
                    aria-label="Invite team members">
                    <CirclePlus color="#BDBDBD" size={24} />
                </div>
            )}

            <Dialog open={isOpen} onOpenChange={clearStatesOnClose}>
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle className="text-xl font-semibold">Invite Team Members</DialogTitle>
                        <DialogDescription className="text-gray-600">
                            Add your team members to collaborate on this Workspace. Each will receive an invitation via
                            email.{' '}
                        </DialogDescription>
                    </DialogHeader>
                    <Separator />
                    <div className="flex space-x-2 relative">
                        <Input
                            value={SHAREABLE_LINK}
                            readOnly
                            className="px-5 rounded-sm overflow-hidden truncate whitespace-nowrap"
                            title={SHAREABLE_LINK}
                        />
                        <Button
                            variant="ghost"
                            disabled={linkCopied}
                            onClick={copyLink}
                            type="button"
                            className="absolute right-0 rounded-sm  hover:bg-transparent">
                            <div className="ml-1 transition-transform duration-200 hover:scale-110 hover:opacity-80 cursor-pointer">
                                <Copy size={16} color="#343330" />
                            </div>
                        </Button>
                    </div>
                    <div className="mb-4 flex items-center">
                        <Separator style={{ width: '46%', backgroundColor: '#E9EAEB' }} />
                        <span className="mx-2 text-xs text-gray-500">OR</span>
                        <Separator style={{ width: '46%', backgroundColor: '#E9EAEB' }} />
                    </div>
                    <Form {...form}>
                        <form onSubmit={handleSubmit(onFormSubmit)}>
                            {/* Display email input fields and validation errors */}
                            <InviteEmailFields
                                label="Enter emails *"
                                hasEmptyEmailField={hasEmptyEmailField}
                                duplicateEmail={duplicateEmail}
                            />
                            <DialogFooter className="mt-4">
                                <div className="w-full">
                                    <Button type="submit" disabled={isLoading} className="w-full mb-3">
                                        {isLoading ? <Loader className="animate-spin h-4 w-4 mr-2" /> : 'Invite'}
                                    </Button>
                                    <Button type="button" onClick={clearStatesOnClose} className="w-full" variant="outline">
                                        Cancel
                                    </Button>
                                </div>
                            </DialogFooter>
                        </form>
                    </Form>
                </DialogContent>
            </Dialog>
        </>
    )
}
