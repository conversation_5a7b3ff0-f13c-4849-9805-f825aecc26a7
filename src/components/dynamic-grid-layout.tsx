'use client'

import React, { useState, useEffect } from 'react'

type GridRow = number[] // Example: [30, 70] -> two columns with 30% and 70%

interface DynamicGridLayoutProps {
    layout: GridRow[] // Array of rows with column widths
    items: React.ReactNode[] // Cards or any content
}

export default function DynamicGridLayout({ layout, items }: DynamicGridLayoutProps) {
    const [isMobile, setIsMobile] = useState(false)
    let itemIndex = 0

    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.innerWidth < 768)
        }

        // Check initial size
        checkMobile()

        // Add resize listener
        window.addEventListener('resize', checkMobile)

        return () => window.removeEventListener('resize', checkMobile)
    }, [])

    return (
        <div className="w-full space-y-2">
            {layout.map((row, rowIndex) => (
                <div
                    key={rowIndex + 1}
                    className="grid gap-2"
                    style={{
                        gridTemplateColumns: isMobile ? '1fr' : row.map((w) => `${w}%`).join(' '),
                    }}>
                    {row.map((_, colIndex) => {
                        if (itemIndex >= items.length) return null
                        const content = items[itemIndex++]

                        return (
                            <div key={colIndex} className="w-full">
                                {content}
                            </div>
                        )
                    })}
                </div>
            ))}
        </div>
    )
}
