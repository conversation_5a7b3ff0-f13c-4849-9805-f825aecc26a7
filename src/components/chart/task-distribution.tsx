'use client'

import * as React from 'react'
import { Loader } from 'lucide-react'
import { <PERSON>, Pie, PieChart } from 'recharts'

import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart'
import { formatDate } from '@/utils/format-date.utils'

interface ChartDataItem {
    id: number
    label: string
    completed_tasks_count: number
    total_tasks_count: number
    completion_percentage: number | string
    colour: {
        text: string
        border: string
        background: string
    }
    trend_message: string
    calculated_date_from: string
    calculated_date_to: string
    is_top_department: boolean
    top_department_text: string
}

export function TaskDistributionChart({ data, isDataLoading }: { data: ChartDataItem[]; isDataLoading: boolean }) {
    const chartData = React.useMemo(
        () =>
            data?.map((item) => ({
                name: item.label,
                value: item.total_tasks_count || 0,
                fill: item.colour.background,
                ...item,
            })),
        [data],
    )

    const chartConfig: ChartConfig = {
        department: {
            label: 'Department',
        },
    }
    const trendMessage = chartData?.length
        ? chartData
              .filter((item) => item.is_top_department)
              .map((item) => item.label + ' ' + item.completion_percentage + '%')
              .join(', ')
        : ''
    const activeDepartments = chartData?.length

    if (isDataLoading) {
        return (
            <div className="flex min-h-[250px] md:min-h-[350px] lg:min-h-[400px] items-center justify-center w-full">
                <Loader className="h-6 w-6 animate-spin text-gray-400" />
            </div>
        )
    }

    return (
        <Card className="flex flex-col bg-transparent shadow-none border-0 gap-0 w-full">
            <CardHeader className="items-center pb-2 md:pb-4 px-2 md:px-6">
                <CardTitle className="text-base md:text-lg lg:text-xl text-center">Task Distribution by Department</CardTitle>
                <CardDescription className="text-center">
                    {chartData?.length > 0 && (
                        <span className="text-xs md:text-sm text-muted-foreground">
                            {formatDate(chartData[0]?.calculated_date_from, 'MM-YYYY')} -{' '}
                            {formatDate(chartData[0]?.calculated_date_to, 'MM-YYYY')}
                        </span>
                    )}
                </CardDescription>
            </CardHeader>
            <CardContent className="flex-1 px-2 md:px-4 lg:px-6">
                <ChartContainer
                    config={chartConfig}
                    className="mx-auto aspect-square w-full max-w-[250px] sm:max-w-[300px] md:max-w-[350px] lg:max-w-[380px] h-[200px] sm:h-[240px] md:h-[270px] lg:h-[300px]">
                    {chartData?.length > 0 ? (
                        <PieChart width={100} height={100}>
                            <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
                            <Pie
                                data={chartData}
                                dataKey="value"
                                nameKey="name"
                                innerRadius="50%"
                                outerRadius="80%"
                                strokeWidth={2}
                                isAnimationActive={false}>
                                <Label
                                    content={({ viewBox }) => {
                                        if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                                            return (
                                                <text x={viewBox.cx} y={viewBox.cy} textAnchor="middle" dominantBaseline="middle">
                                                    <tspan
                                                        x={viewBox.cx}
                                                        y={viewBox.cy}
                                                        className="fill-foreground text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold">
                                                        {activeDepartments?.toLocaleString()}
                                                    </tspan>
                                                    <tspan
                                                        x={viewBox.cx}
                                                        y={(viewBox.cy || 0) + 18}
                                                        className="fill-muted-foreground text-xs sm:text-sm">
                                                        Active Departments
                                                    </tspan>
                                                </text>
                                            )
                                        }
                                        return null
                                    }}
                                />
                            </Pie>
                        </PieChart>
                    ) : (
                        <div className="flex h-full items-center justify-center">
                            <p className="text-gray-500 text-sm md:text-base">No Tasks available</p>
                        </div>
                    )}
                </ChartContainer>
            </CardContent>
            <CardFooter className="flex-col gap-1 md:gap-2 text-xs md:text-sm px-2 md:px-4 lg:px-6 pb-2 md:pb-4">
                <div className="flex items-center justify-center gap-2 font-medium leading-none text-center w-full">
                    <span className="break-words">{trendMessage}</span>
                </div>
                <div className="leading-none text-muted-foreground text-center">Showing total tasks for the last 6 months</div>
            </CardFooter>
        </Card>
    )
}
