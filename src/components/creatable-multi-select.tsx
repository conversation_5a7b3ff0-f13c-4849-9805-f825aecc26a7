'use client'

import * as React from 'react'
import { X, Check, Plus, PlusIcon } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
    CommandSeparator,
} from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

export interface MultiSelectProps {
    predefinedOptions: string[]
    defaultSelected?: string[]
    onChange?: (selected: string[]) => void
    placeholder?: string
    className?: string
}

export function MultiSelect({
    predefinedOptions,
    defaultSelected = [],
    onChange,
    placeholder = 'Select options...',
    className,
}: MultiSelectProps) {
    const [open, setOpen] = React.useState(false)
    const [selected, setSelected] = React.useState<string[]>(defaultSelected)
    const [inputValue, setInputValue] = React.useState('')

    const handleUnselect = (item: string) => {
        const filtered = selected.filter((i) => i !== item)
        setSelected(filtered)
        onChange?.(filtered)
    }

    const handleSelect = (item: string) => {
        const newSelected = [...selected, item]
        setSelected(newSelected)
        onChange?.(newSelected)
        setInputValue('')
    }

    const handleAddCustomValue = () => {
        // Check if input matches any predefined option (case-insensitive)
        const isExistingOption = predefinedOptions.some((option) => option.toLowerCase() === inputValue.toLowerCase())

        if (inputValue && !selected.includes(inputValue) && !isExistingOption) {
            const newSelected = [...selected, inputValue]
            setSelected(newSelected)
            onChange?.(newSelected)
            setInputValue('')
        }
    }

    const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
        if (e.key === 'Enter' && inputValue) {
            e.preventDefault()
            // Check if input matches any predefined option (case-insensitive)
            const isExistingOption = predefinedOptions.some((option) => option.toLowerCase() === inputValue.toLowerCase())

            if (!isExistingOption) {
                handleAddCustomValue()
            }
        }
    }

    // Filter options that are not already selected
    const filteredOptions = predefinedOptions.filter((option) => !selected.includes(option))

    return (
        <div className={cn('relative', className)}>
            <Popover open={open} onOpenChange={setOpen}>
                <div className="flex items-center w-full">
                    <div
                        role="combobox"
                        aria-expanded={open}
                        aria-controls="combobox-options"
                        className={cn(
                            'flex-1 justify-between border items-center rounded-md',
                            selected.length > 0 ? 'h-auto min-h-[39px]' : 'h-[39px]',
                        )}>
                        <div className="flex flex-wrap gap-1 items-center px-2 pt-2 pb-1">
                            {selected.length > 0 ? (
                                selected.map((item) => (
                                    <Badge
                                        variant="secondary"
                                        key={item}
                                        className="mr-1 mb-1 rounded-sm border border-gray-200"
                                        onClick={(e) => {
                                            e.stopPropagation()
                                            handleUnselect(item)
                                        }}>
                                        {item}
                                        <p
                                            className="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2"
                                            onKeyDown={(e) => {
                                                if (e.key === 'Enter') {
                                                    handleUnselect(item)
                                                }
                                            }}
                                            onMouseDown={(e) => {
                                                e.preventDefault()
                                                e.stopPropagation()
                                            }}
                                            onClick={(e) => {
                                                e.stopPropagation()
                                                handleUnselect(item)
                                            }}>
                                            <X className="h-3 w-3 text-muted-foreground hover:text-foreground cursor-pointer" />
                                        </p>
                                    </Badge>
                                ))
                            ) : (
                                <p className="text-[#717680] text-xs ">{placeholder}</p>
                            )}
                        </div>
                    </div>
                    <PopoverTrigger asChild className="ml-4">
                        <Button variant="outline" size="icon" className="h-10 w-10 p-0">
                            <PlusIcon className="h-4 w-4" />
                        </Button>
                    </PopoverTrigger>
                </div>
                <PopoverContent className="w-[300px] p-0" align="end" side="top">
                    <Command onKeyDown={handleKeyDown}>
                        <CommandInput placeholder="Search options..." value={inputValue} onValueChange={setInputValue} />
                        <CommandList>
                            <CommandEmpty>
                                {inputValue ? (
                                    <div className="py-2 px-1">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="w-full justify-start"
                                            onClick={handleAddCustomValue}>
                                            <Plus className="mr-2 h-4 w-4" />
                                            {`Add ${inputValue}`}
                                        </Button>
                                    </div>
                                ) : (
                                    <p className="py-2 px-1 text-center text-sm">No options found.</p>
                                )}
                            </CommandEmpty>
                            {filteredOptions.length > 0 && (
                                <CommandGroup heading="Available options">
                                    {filteredOptions.map((option) => (
                                        <CommandItem
                                            key={option}
                                            value={option}
                                            onSelect={() => {
                                                handleSelect(option)
                                            }}>
                                            <Check
                                                className={cn(
                                                    'mr-2 h-4 w-4',
                                                    selected.includes(option) ? 'opacity-100' : 'opacity-0',
                                                )}
                                            />
                                            {option}
                                        </CommandItem>
                                    ))}
                                </CommandGroup>
                            )}
                            {inputValue && !predefinedOptions.includes(inputValue) && !selected.includes(inputValue) && (
                                <>
                                    <CommandSeparator />
                                    <CommandGroup heading="Add custom option">
                                        <CommandItem
                                            onSelect={() => {
                                                handleAddCustomValue()
                                                setOpen(false)
                                            }}>
                                            <Plus className="mr-2 h-4 w-4" />
                                            {`Add ${inputValue}`}
                                        </CommandItem>
                                    </CommandGroup>
                                </>
                            )}
                        </CommandList>
                    </Command>
                </PopoverContent>
            </Popover>
        </div>
    )
}
