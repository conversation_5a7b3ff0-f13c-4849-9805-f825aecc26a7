import { memo, useState, useCallback } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { DnDTaskTypeWithStringId } from '@/app/app/planner/list-view'
import { Status, StatusSelect } from './select-status'
import endpoints from '@/services/api-endpoints'
import AddUsersModal from './add-users.modal'
import { Calendar as CalendarIcon, Trash2, UserPlus } from 'lucide-react'
import { Popover, PopoverTrigger, PopoverContent } from './ui/popover'
import { Calendar } from './ui/calendar'
import { Priority, PrioritySelect } from '@/app/app/task/[id]/task-components/select-task-priority'
import { Checkbox } from './ui/checkbox'
import { formatDate } from '@/utils/format-date.utils'
import { ConfirmAlert } from './confirm-alert'

export interface BulkUpdateData {
    statusId?: number
    assigneeIds?: number[]
    dueDate?: Date
    priorityId?: number
}

interface SelectionBannerProps {
    selectedCount: number
    onClear: () => void
    itemType?: string
    className?: string
    showActions?: boolean
    data?: DnDTaskTypeWithStringId[]
    onBulkUpdate?: (updateData: BulkUpdateData, selectedItems: DnDTaskTypeWithStringId[]) => Promise<void>
    onBulkDelete?: (selectedItems: DnDTaskTypeWithStringId[]) => Promise<void>
}

const SelectionBanner = memo(
    ({
        selectedCount,
        onClear,
        itemType = 'task',
        className = '',
        showActions = true,
        data = [],
        onBulkUpdate,
        onBulkDelete,
    }: SelectionBannerProps) => {
        const [bulkUpdateData, setBulkUpdateData] = useState<BulkUpdateData>({})
        const [isUpdating, setIsUpdating] = useState(false)
        const [isAlertOpen, setIsAlertOpen] = useState(false)
        // const [assigneesInSelectedTasks, setAssigneesInSelectedTasks] = useState<number[]>([])
        const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false)
        // useEffect(() => {
        //     setAssigneesInSelectedTasks(collectAssignees())
        // }, [data])

        // const collectAssignees = useCallback(() => {
        //     if (!data.length) return []
        //     return Array.from(new Set(data.flatMap((task) => task.taskAssignees?.map((assignee) => assignee.id) || [])))
        // }, [data])

        // Helper function to update bulk data
        const updateBulkData = useCallback((key: keyof BulkUpdateData, value: unknown) => {
            setBulkUpdateData((prev) => ({
                ...prev,
                [key]: value,
            }))
        }, [])

        // Handle status change
        const handleStatusChange = useCallback(
            (statusData: Status) => {
                updateBulkData('statusId', statusData?.id || statusData)
            },
            [updateBulkData],
        )

        // Handle assignee change
        const handleAssigneeChange = useCallback(
            (assigneeData: number[]) => {
                // Assuming assigneeData contains array of user IDs or user objects
                const assigneeIds = assigneeData
                updateBulkData('assigneeIds', assigneeIds)
            },
            [updateBulkData],
        )

        // Handle due date change
        const handleDueDateChange = useCallback(
            (date: Date | undefined) => {
                updateBulkData('dueDate', date)
            },
            [updateBulkData],
        )

        // Handle priority change
        const handlePriorityChange = useCallback(
            (priorityData: Priority) => {
                updateBulkData('priorityId', priorityData?.id || priorityData)
            },
            [updateBulkData],
        )
        // Clear bulk update data when selection is cleared
        const handleClear = useCallback(() => {
            setBulkUpdateData({})
            onClear()
        }, [onClear])

        // Handle bulk update
        const handleUpdate = useCallback(async () => {
            if (!onBulkUpdate || !data.length) return

            // Filter out undefined values
            const cleanUpdateData = Object.fromEntries(
                Object.entries(bulkUpdateData).filter(([, value]) => value !== undefined),
            ) as BulkUpdateData

            // Only proceed if there's something to update
            if (Object.keys(cleanUpdateData).length === 0) {
                return
            }

            setIsUpdating(true)
            try {
                await onBulkUpdate(cleanUpdateData, data)
                // Clear the update data after successful update
                handleClear()
                // Optionally clear selection after update
                // onClear()
            } catch (error) {
                throw error
                // Handle error (show toast, etc.)
            } finally {
                setIsUpdating(false)
            }
        }, [bulkUpdateData, data, onBulkUpdate, handleClear])

        // Handle delete action
        const handleDelete = useCallback(async () => {
            if (!data.length) return

            if (!onBulkDelete) return
            try {
                await onBulkDelete(data)
                // Clear the update data after successful update
                handleClear()
                // Optionally clear selection after update
                // onClear()
            } catch (error) {
                throw error
                // Handle error (show toast, etc.)
            } finally {
                setIsUpdating(false)
            }
        }, [data, onBulkDelete, handleClear])

        const pluralizedItemType = selectedCount > 1 ? `${itemType}s` : itemType
        const hasChanges = Object.keys(bulkUpdateData).some((key) => bulkUpdateData[key as keyof BulkUpdateData] !== undefined)

        return (
            <div className={`p-2 ${className}`}>
                <div className="flex items-center gap-2 ">
                    <div className="flex items-center gap-2">
                        <Checkbox checked onClick={handleClear} />
                    </div>

                    {showActions && (
                        <div className="flex items-center justify-center gap-1  w-full pl-1">
                            <StatusSelect
                                entityId={1}
                                entityType="task"
                                initialStatusId={0}
                                fetchEndpoint={endpoints.meta.getStatuses}
                                className="w-[125px] h-[29px] rounded-[6px] border border-[#D5D7DA] bg-[#FFFFFF] pl-1"
                                onStatusChange={handleStatusChange}
                            />

                            <PrioritySelect
                                initialPriorityId={0}
                                fetchEndpoint={endpoints.meta.getPriorities}
                                className="w-[120px] h-[29px] rounded-[6px] border border-[#D5D7DA] bg-[#FFFFFF] pl-2"
                                onPriorityChange={handlePriorityChange}
                            />

                            <AddUsersModal
                                dialogueTitle="Add Assignees"
                                dialogueDescription="Add assignees to the selected tasks."
                                onSubmit={(users, closeModal) => {
                                    handleAssigneeChange(users)
                                    closeModal()
                                }}
                                alreadyAssignedUsers={bulkUpdateData.assigneeIds || []}>
                                <div className="flex w-[120px] h-[29px] rounded-[6px] border border-[#D5D7DA] bg-[#FFFFFF] pl-2 items-center justify-between px-2">
                                    <span className="text-[13px]">
                                        Assignee
                                        {bulkUpdateData.assigneeIds?.length ? ` (${bulkUpdateData.assigneeIds.length})` : ''}
                                    </span>
                                    <UserPlus size={16} color="#5B6871" />
                                </div>
                            </AddUsersModal>

                            <div className="text-sm">
                                <Popover>
                                    <PopoverTrigger className="cursor-pointer flex w-[120px] h-[29px] rounded-[6px] border border-[#D5D7DA] bg-[#FFFFFF] pl-2 items-center justify-between px-2">
                                        {bulkUpdateData.dueDate ? (
                                            <>{formatDate(bulkUpdateData.dueDate, 'DD-MM-YYYY')}</>
                                        ) : (
                                            <span className="text-[13px]">Due Date</span>
                                        )}
                                        <CalendarIcon size={16} color="#5B6871" />
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0" align="start">
                                        <Calendar
                                            mode="single"
                                            disabled={(date) => date < new Date()}
                                            selected={bulkUpdateData.dueDate}
                                            onSelect={handleDueDateChange}
                                            initialFocus
                                        />
                                    </PopoverContent>
                                </Popover>
                            </div>

                            <ConfirmAlert
                                onConfirm={handleDelete}
                                setIsAlertOpen={setIsDeleteAlertOpen}
                                isAlertOpen={isDeleteAlertOpen}>
                                <Button size="icon" variant="ghost" disabled={isUpdating}>
                                    <Trash2 size={18} color="#5B6871" />
                                </Button>
                            </ConfirmAlert>

                            <ConfirmAlert onConfirm={handleUpdate} setIsAlertOpen={setIsAlertOpen} isAlertOpen={isAlertOpen}>
                                <Button
                                    className="max-w-[83px] h-[30px] text-[13px] px-2 -ml-2"
                                    disabled={!hasChanges || isUpdating || data.length === 0}>
                                    {isUpdating ? 'Updating...' : 'Update'}
                                </Button>
                            </ConfirmAlert>
                        </div>
                    )}
                </div>
                <div className="mt-2 p-2 bg-yellow-100 rounded text-xs">
                    {/* <strong>Pending changes:</strong> {JSON.stringify(bulkUpdateData, null, 2)} */}
                    <span className="text-sm text-gray-600">
                        {selectedCount} {pluralizedItemType} selected
                    </span>
                </div>
            </div>
        )
    },
)

SelectionBanner.displayName = 'SelectionBanner'

export default SelectionBanner
