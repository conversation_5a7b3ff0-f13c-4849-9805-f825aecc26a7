'use client'

import * as React from 'react'
import { Check, ChevronDown, Folder, Search } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Input } from './ui/input'

interface DropdownItem {
    id: string
    title: string
    createdOn: string
    type?: 'folder' | 'item'
}

interface SearchableDropdownProps {
    title: string
    createdOn: string
    items: DropdownItem[]
    onSelect?: (item: DropdownItem) => void
}

export function SearchableDropdown({ title, createdOn, items, onSelect }: SearchableDropdownProps) {
    const [isOpen, setIsOpen] = React.useState(false)
    const [searchQuery, setSearchQuery] = React.useState('')
    const [selectedItem, setSelectedItem] = React.useState<DropdownItem | null>(null)
    const dropdownRef = React.useRef<HTMLDivElement>(null)

    const filteredItems = React.useMemo(() => {
        return items.filter((item) => item.title.toLowerCase().includes(searchQuery.toLowerCase()))
    }, [items, searchQuery])

    const handleSelect = (item: DropdownItem) => {
        setSelectedItem(item)
        if (onSelect) {
            onSelect(item)
        }
        setIsOpen(false)
    }

    // Close dropdown when clicking outside
    React.useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false)
            }
        }

        document.addEventListener('mousedown', handleClickOutside)
        return () => {
            document.removeEventListener('mousedown', handleClickOutside)
        }
    }, [])

    return (
        <div ref={dropdownRef} className="w-full mx-auto rounded-lg border border-gray-200 shadow-sm relative">
            {/* Header */}
            <div className="flex items-center justify-between p-4 cursor-pointer" onClick={() => setIsOpen(!isOpen)}>
                <div className="flex items-center gap-3">
                    <Folder className="h-5 w-5 text-gray-500" />
                    <div>
                        <h3 className="font-medium text-sm text-gray-900">{title}</h3>
                        <p className="text-xs text-gray-500">Created on {createdOn}</p>
                    </div>
                </div>
                <ChevronDown className={cn('h-5 w-5 text-gray-500 transition-transform duration-200', isOpen && 'rotate-180')} />
            </div>

            {/* Dropdown Content */}
            {isOpen && (
                <div className="absolute top-20 left-0 right-0 z-10 w-full border-t border-gray-200 p-4 bg-white shadow-md rounded-lg">
                    <h4 className="font-medium mb-3 text-xs">History</h4>

                    {/* Search Input */}
                    <div className="relative mb-3">
                        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                            type="text"
                            placeholder="What are you looking for?"
                            className="w-full rounded-md border border-gray-200 py-2 pl-10 pr-4 text-xs focus:border-primary"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                        />
                    </div>

                    {/* Dropdown Items */}
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                        {filteredItems.map((item) => (
                            <div
                                key={item.id}
                                className={cn(
                                    'flex items-start justify-between p-2 rounded-md cursor-pointer hover:bg-gray-50',
                                    selectedItem?.id === item.id && 'bg-blue-50',
                                )}
                                onClick={() => handleSelect(item)}>
                                <div>
                                    <p className="font-medium text-sm text-gray-900">{item.title}</p>
                                    <p className="text-[10px] text-gray-500">Created on {item.createdOn}</p>
                                </div>
                                {selectedItem?.id === item.id && <Check className="h-4 w-4 text-blue-600" />}
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    )
}
