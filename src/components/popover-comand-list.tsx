'use client'

import * as React from 'react'
import { useInfiniteQuery } from '@tanstack/react-query'
import { Check, ChevronsUpDown, Loader2, X } from 'lucide-react'
import { cn } from '@/lib/utils'

import { Button } from '@/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'

// Define the ref type with the methods we want to expose
export type PaginatedComboBoxRef = {
    removeItem: (item: Record<string, unknown>) => void
}

export type PaginatedComboboxProps<T> = {
    endpoint: string
    onSelectionChange: (selectedItems: T[]) => void
    multiple?: boolean
    returnKeys?: string[]
    placeholder?: string
    disabled?: boolean
    className?: string
    triggerClassName?: string
    contentClassName?: string
    itemClassName?: string
    pageSize?: number
    labelKey?: string
    valueKey?: string
    initialSelected?: T[]
    params?: Record<string, unknown>
    icon?: React.ReactNode
    dataAccessor?: string
    searchPlaceholder?: string
    showBadges?: boolean
    searchParamName?: string
    labelModifier?: (label: string, item: T) => string | React.ReactNode
    showSelectedData?: boolean
    showNoMoreItems?: boolean
}

export const PaginatedComboBox = React.forwardRef<PaginatedComboBoxRef, PaginatedComboboxProps<Record<string, unknown>>>(
    function PaginatedComboBox(
        {
            endpoint,
            onSelectionChange,
            multiple = false,
            returnKeys,
            placeholder = 'Select an item',
            disabled = false,
            className,
            triggerClassName,
            contentClassName,
            itemClassName,
            pageSize = 10,
            labelKey = 'name',
            valueKey = 'id',
            initialSelected = [],
            params,
            icon,
            dataAccessor = 'projectData',
            searchPlaceholder = 'Search...',
            showBadges = false,
            searchParamName = 'searchQuery',
            labelModifier,
            showSelectedData = false,
            showNoMoreItems = false,
        },
        ref,
    ) {
        const [open, setOpen] = React.useState(false)
        const [selectedItems, setSelectedItems] = React.useState<Record<string, unknown>[]>(initialSelected)
        const [searchQuery, setSearchQuery] = React.useState('')

        // Fetch data using React Query
        const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, isError, error } = useInfiniteQuery({
            queryKey: ['infinite-select', endpoint, searchQuery],
            initialPageParam: 1,
            queryFn: async ({ pageParam = 1 }) => {
                try {
                    const response = await api.get(endpoint, {
                        params: {
                            ...params,
                            page: pageParam,
                            limit: pageSize,
                            [searchParamName]: searchQuery || undefined,
                        },
                    })
                    return response.data.data
                } catch (error) {
                    toast.error('Error fetching data. Please try again later.')
                    throw error
                }
            },
            getNextPageParam: (lastPage) => {
                const { paginationData } = lastPage
                return paginationData?.currentPage < paginationData?.totalPages ? paginationData?.currentPage + 1 : undefined
            },
            enabled: open,
        })

        // Handle scroll to implement infinite loading
        const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
            const { scrollTop, scrollHeight, clientHeight } = e.currentTarget

            if (scrollHeight - scrollTop <= clientHeight * 1.5) {
                if (hasNextPage && !isFetchingNextPage) {
                    fetchNextPage()
                }
            }
        }

        // Process all items from all pages
        const allItems = React.useMemo(() => {
            if (!data) return []
            const isContainArray = data?.pages[0]?.length

            const allOptions = isContainArray
                ? data?.pages.flatMap((page) => page) || []
                : data?.pages?.flatMap((page) => page?.[dataAccessor]) || []
            return allOptions
        }, [data, dataAccessor])

        // Helper function to get nested property value using path string like "workspace.name"
        const getNestedValue = (obj: unknown, path: string) => {
            return path.split('.').reduce((prev, curr) => (prev ? (prev as Record<string, unknown>)[curr] : null), obj)
        }

        // Handle selection change
        const handleSelect = (value: string) => {
            if (!allItems.length) return

            // Find the selected item
            const selectedItem = allItems.find((item) => String(getNestedValue(item, valueKey)) === value)
            if (!selectedItem) return

            if (multiple) {
                // For multiple selection, toggle the item
                const isAlreadySelected = selectedItems.some((item) => String(getNestedValue(item, valueKey)) === value)

                const newSelectedItems = isAlreadySelected
                    ? selectedItems.filter((item) => String(getNestedValue(item, valueKey)) !== value)
                    : [...selectedItems, selectedItem]

                setSelectedItems(newSelectedItems)

                // Return only the specified keys if returnKeys is provided
                if (returnKeys && returnKeys.length > 0) {
                    const transformedItems = newSelectedItems.map((item) => {
                        const result: Record<string, unknown> = {}
                        returnKeys.forEach((key) => {
                            // Handle nested keys
                            result[key] = getNestedValue(item, key)
                        })
                        return result as Record<string, unknown>
                    })

                    onSelectionChange(transformedItems)
                } else {
                    onSelectionChange(newSelectedItems)
                }
            } else {
                // For single selection, replace the selection
                setSelectedItems([selectedItem])
                setOpen(false)

                // Return only the specified keys if returnKeys is provided
                if (returnKeys && returnKeys.length > 0) {
                    const result: Record<string, unknown> = {}
                    returnKeys.forEach((key) => {
                        // Handle nested keys
                        result[key] = getNestedValue(selectedItem, key)
                    })
                    onSelectionChange([result as Record<string, unknown>])
                } else {
                    onSelectionChange([selectedItem])
                }
            }
        }

        // Remove a selected item (for multiple selection)
        const removeItem = (itemToRemove: Record<string, unknown>) => {
            const newSelectedItems = selectedItems.filter(
                (item) => String(getNestedValue(item, valueKey)) !== String(getNestedValue(itemToRemove, valueKey)),
            )
            setSelectedItems(newSelectedItems)

            if (returnKeys && returnKeys.length > 0) {
                const transformedItems = newSelectedItems.map((item) => {
                    const result: Record<string, unknown> = {}
                    returnKeys.forEach((key) => {
                        result[key] = getNestedValue(item, key)
                    })
                    return result
                })
                onSelectionChange(transformedItems)
            } else {
                onSelectionChange(newSelectedItems)
            }
        }

        // Expose the removeItem method to the parent component
        React.useImperativeHandle(ref, () => ({
            removeItem,
        }))

        // Display value in the trigger
        const displayValue = React.useMemo(() => {
            if (!selectedItems.length) return ''

            if (multiple) {
                return selectedItems.map((item) => String(getNestedValue(item, labelKey))).join(', ')
            } else {
                return String(getNestedValue(selectedItems[0], labelKey))
            }
        }, [selectedItems, labelKey, multiple])

        return (
            <div className={cn('relative w-full', className)}>
                <Popover open={open} onOpenChange={setOpen} modal>
                    <PopoverTrigger asChild>
                        <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={open}
                            disabled={disabled || isLoading}
                            className={cn('w-full justify-end', isLoading && 'opacity-70 cursor-not-allowed', triggerClassName)}>
                            {showSelectedData && (
                                <>
                                    {icon && icon}
                                    {multiple && selectedItems.length > 0 ? (
                                        <div className="flex flex-wrap gap-1 max-w-[calc(100%-20px)]">
                                            {selectedItems.map((item, index) => {
                                                const label = getNestedValue(item, labelKey)
                                                return (
                                                    <div
                                                        key={String(getNestedValue(item, valueKey)) || index}
                                                        className="flex items-center gap-1">
                                                        {labelModifier ? (
                                                            labelModifier(String(label), item)
                                                        ) : (
                                                            <Badge
                                                                key={index}
                                                                variant="secondary"
                                                                className="truncate max-w-[150px]">
                                                                {String(label)}
                                                                <X
                                                                    className="h-3 w-3 cursor-pointer"
                                                                    onClick={(e) => {
                                                                        e.stopPropagation()
                                                                        removeItem(item)
                                                                    }}
                                                                />
                                                            </Badge>
                                                        )}
                                                    </div>
                                                )
                                            })}
                                            {/* {selectedItems.length > 2 && <Badge variant="secondary">+{selectedItems.length - 2}</Badge>} */}
                                        </div>
                                    ) : (
                                        <span className="truncate">{displayValue || placeholder}</span>
                                    )}
                                </>
                            )}
                            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent
                        className={cn('p-0', contentClassName)}
                        style={{ width: 'var(--radix-popover-trigger-width)' }}
                        align="start"
                        forceMount>
                        <Command shouldFilter={false}>
                            <CommandInput placeholder={searchPlaceholder} value={searchQuery} onValueChange={setSearchQuery} />
                            <CommandList>
                                <ScrollArea className="max-h-[300px] overflow-auto" onScroll={handleScroll}>
                                    <CommandEmpty>
                                        {isLoading ? (
                                            <div className="flex items-center justify-center p-2">
                                                <Loader2 className="h-4 w-4 animate-spin" />
                                            </div>
                                        ) : (
                                            'No items found'
                                        )}
                                    </CommandEmpty>
                                    <CommandGroup>
                                        {isError ? (
                                            <div className="p-2 text-center text-destructive">
                                                Error: {(error as Error).message}
                                            </div>
                                        ) : allItems.length > 0 && allItems[0] ? (
                                            allItems.map((item) => {
                                                const value = String(getNestedValue(item, valueKey))
                                                const label = String(getNestedValue(item, labelKey))
                                                const isSelected = selectedItems.some(
                                                    (selected) => String(getNestedValue(selected, valueKey)) === value,
                                                )

                                                return (
                                                    <CommandItem
                                                        key={value}
                                                        value={value}
                                                        onSelect={handleSelect}
                                                        className={cn(
                                                            'flex items-center justify-between',
                                                            isSelected && 'font-medium',
                                                            itemClassName,
                                                            'z-auto',
                                                        )}>
                                                        <span>{label}</span>
                                                        {isSelected && <Check className="h-4 w-4 ml-2" />}
                                                    </CommandItem>
                                                )
                                            })
                                        ) : null}

                                        {isFetchingNextPage && (
                                            <div className="p-2 text-center">
                                                <Loader2 className="h-4 w-4 animate-spin mx-auto" />
                                            </div>
                                        )}

                                        {showNoMoreItems && !hasNextPage && allItems.length > 0 && !isLoading && allItems[0] && (
                                            <div className="p-2 text-center text-muted-foreground text-xs">No more items</div>
                                        )}
                                    </CommandGroup>
                                </ScrollArea>
                            </CommandList>
                        </Command>
                    </PopoverContent>
                </Popover>

                {/* Display selected items as badges for multiple selection */}
                {multiple && showBadges && selectedItems.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                        {selectedItems.map((item, index) => (
                            <Badge key={index} variant="secondary" className="flex items-center gap-1">
                                {String(getNestedValue(item, labelKey))}
                                <X
                                    className="h-3 w-3 cursor-pointer"
                                    onClick={(e) => {
                                        e.stopPropagation()
                                        removeItem(item)
                                    }}
                                />
                            </Badge>
                        ))}
                    </div>
                )}
            </div>
        )
    },
)
