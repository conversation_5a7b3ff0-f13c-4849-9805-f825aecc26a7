import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import { generateFallbackLetters } from '@/utils/generate-fallback-letters.utils'

interface AssigneeAvatarProps {
    assignee: string
    className?: string
    imageUrl?: string
    fallbackClassName?: string
    showTooltip?: boolean
}

export const AssigneeAvatar: React.FC<AssigneeAvatarProps> = ({
    assignee,
    className = 'h-6 w-6',
    imageUrl = '',
    fallbackClassName = 'text-xs',
    showTooltip = true,
}) => {
    return (
        <Tooltip>
            <TooltipTrigger asChild>
                <Avatar className={className}>
                    {imageUrl && <AvatarImage src={imageUrl} alt={assignee} />}
                    <AvatarFallback className={fallbackClassName}>{generateFallbackLetters(assignee)}</AvatarFallback>
                </Avatar>
            </TooltipTrigger>
            {showTooltip && (
                <TooltipContent>
                    <p>{assignee}</p>
                </TooltipContent>
            )}
        </Tooltip>
    )
}
