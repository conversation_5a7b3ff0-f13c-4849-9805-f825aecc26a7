import { useState } from 'react'
import { CommentType } from '.'
import CommentInput from './comment-input'

const EditComment = ({
    comment,
    editComment,
}: {
    comment: CommentType
    editComment: (data: { id: number; content: string }) => void
}) => {
    const [commentContent, setCommentContent] = useState(comment.content)

    const handleEditSubmit = () => {
        if (!commentContent.trim()) return
        editComment({ id: comment.id, content: commentContent })
    }
    const handleEditChange = (value: string) => {
        if (value.trim()) {
            setCommentContent(value)
        }
    }

    return (
        <CommentInput
            value={commentContent}
            onChange={handleEditChange}
            onSubmit={handleEditSubmit}
            user={comment.creator}
            isLoading={false}
            showAvatar={false}
        />
    )
}

export default EditComment
