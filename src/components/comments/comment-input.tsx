import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Send } from 'lucide-react'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'
import { cn } from '@/lib/utils'

const CommentInput = ({
    value,
    onChange,
    onSubmit,
    user,
    isLoading,
    placeholder = 'Write a comment...',
    showAvatar = true,
    isReply = false,
}: {
    value: string
    onChange: (value: string) => void
    onSubmit: () => void
    user?: { id: number; first_name: string; last_name: string; img_url: string | null }
    isLoading: boolean
    placeholder?: string
    showAvatar?: boolean
    isReply?: boolean
}) => {
    const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault()
            onSubmit()
        }
    }

    return (
        <div className="flex gap-2 mb-2 w-full">
            {showAvatar && (
                <AssigneeAvatar
                    assignee={`${user?.first_name} ${user?.last_name}`}
                    imageUrl={user?.img_url || ''}
                    className="border-2 border-white h-[30px] w-[30px]"
                />
            )}
            <div
                className={cn(
                    'flex-1 flex gap-2 items-end border pr-2 bg-white pb-2 rounded-[10px] min-h-[91px] max-h-[110px] w-full max-w-full',
                    isReply && 'bg-[#F0F2F5] min-h-[48px] rounded-[8px]',
                )}>
                <Textarea
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                    placeholder={placeholder}
                    className="w-full ml-2 border-none shadow-none max-w-[98%] rounded-none px-0 focus-visible:ring-0 focus-visible:border-gray-400 h-full"
                    onKeyDown={handleKeyPress}
                />
                <Button
                    onClick={onSubmit}
                    size="sm"
                    className={cn(
                        'rounded-full w-8 h-8 p-0 bg-gray-900 hover:bg-gray-800',
                        isReply && 'rounded-[8px] w-15 h-7 text-xs',
                    )}
                    disabled={isLoading}>
                    {!isReply ? <Send className="w-4 h-4" /> : 'Reply'}
                </Button>
            </div>
        </div>
    )
}

export default CommentInput
