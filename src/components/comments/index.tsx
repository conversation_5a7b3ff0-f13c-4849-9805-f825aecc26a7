'use client'

import { useState } from 'react'
import { Loader } from 'lucide-react'
import { useAuthStore } from '@/store/auth.store'
import { useMutation, useQuery } from '@tanstack/react-query'
import endpoints from '@/services/api-endpoints'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'
import { Separator } from '@/components/ui/separator'
import CommentInput from './comment-input'
import CommentItem from './comment-item'
import ReplyItem from './reply-item'
import { AxiosResponse } from 'axios'
import { invalidateActivityLogs } from '@/services/invalidate-query.service'
import { ScrollArea } from '../ui/scroll-area'

export interface CommentType {
    id: number
    content: string
    entity_type: 'task' | string
    entity_id: number
    parent_id: number | null
    created_by: number
    created_at: string
    updated_at: string
    deleted_at: string | null
    creator: {
        id: number
        first_name: string
        last_name: string
        img_url: string | null
    }
    upvotes: { user_id: number }[]
    replies: CommentType[]
    time_ago: string
}

const heightConfig: Record<number, string> = {
    0: 'h-0',
    1: 'h-60',
    2: 'h-80',
    3: 'h-100',
    4: 'h-140',
} as const

const Comments = ({ entity_type = 'task', entity_id }: { entity_type?: string; entity_id: string }) => {
    const [newComment, setNewComment] = useState('')
    const [replyingTo, setReplyingTo] = useState<number | null>(null)
    const [replyContent, setReplyContent] = useState('')
    const { user } = useAuthStore()
    const [editingItem, setEditingItem] = useState<CommentType | null>(null)

    // Fetch comments
    const {
        data: comments = [],
        isLoading,
        refetch,
    } = useQuery({
        queryKey: ['comments', entity_id],
        queryFn: async () => {
            const url = endpoints.comments.getComments.replace(':entityType', entity_type).replace(':entityId', entity_id)
            const response = await api.get(url)
            return response.data.data
        },
        enabled: !!entity_id,
    })

    // Add comment mutation
    const addCommentMutation = useMutation({
        mutationFn: async (data: { entity_id: number; content: string; entity_type: string; parent_id?: number }) => {
            const response = await api.post(endpoints.comments.createComment, data)
            return response.data
        },
        onSuccess: () => {
            toast.success('Comment added successfully')
            setNewComment('')
            setReplyContent('')
            setReplyingTo(null)
            refetch()
            invalidateActivityLogs()
        },
        onError: (er) => {
            toast.error('Failed to add comment')
            console.log(er)
        },
    })
    const editCommentMutation = useMutation({
        mutationFn: async (data: { id: number; content: string }) => {
            const url = endpoints.comments.updateComment.replace(':commentId', data.id.toString())
            const response = await api.patch(url, data)
            return response.data
        },
        onSuccess: () => {
            toast.success('Comment edited successfully')
            setEditingItem(null)
            refetch()
            invalidateActivityLogs()
        },
        onError: () => {
            toast.error('Failed to edit comment')
        },
    })

    const upvoteMutation = useMutation({
        mutationFn: async (commentId: number) => {
            const url = endpoints.comments.updateComment.replace(':commentId', commentId.toString()) + '/upvote'
            const response = await api.post(url)
            return response.data
        },
        onSuccess: (data: AxiosResponse) => {
            const upvoteResponse = data?.data
            if (upvoteResponse?.upvoted) {
                toast.success('Comment upvoted successfully')
            } else {
                toast.success('Upvote removed successfully')
            }
            refetch()
            invalidateActivityLogs()
        },
        onError: () => {
            toast.error('Failed to upvote comment')
        },
    })

    const onAfterDeleteSuccess = () => {
        toast.success('Comment deleted successfully')
        refetch()
        invalidateActivityLogs()
    }

    const handleAddComment = () => {
        if (newComment.trim()) {
            addCommentMutation.mutate({
                entity_id: Number(entity_id),
                content: newComment,
                entity_type: entity_type,
            })
        }
    }

    const handleAddReply = (parentId: number) => {
        if (replyContent.trim()) {
            addCommentMutation.mutate({
                entity_id: Number(entity_id),
                content: replyContent,
                entity_type: entity_type,
                parent_id: parentId,
            })
        }
    }

    const handleUpvote = (commentId: number) => {
        upvoteMutation.mutate(commentId)
    }
    const handleCancelReply = () => {
        setReplyingTo(null)
        setReplyContent('')
    }

    if (isLoading) {
        return (
            <div className="flex items-center justify-center p-4">
                <Loader className="animate-spin" />
            </div>
        )
    }

    return (
        <div className="w-full mx-auto bg-transparent">
            <div className="flex justify-between items-center mb-2">
                <p className="text-sm font-semibold text-[#000000]">
                    Discussion
                    <span className="rounded-[8px] border px-[5px] py-[1px] border-[#08B38B70] ml-1 bg-[#08B38B08]">
                        {comments?.length}
                    </span>
                </p>
            </div>

            {/* Add new comment */}
            <CommentInput
                value={newComment}
                onChange={setNewComment}
                onSubmit={handleAddComment}
                user={user || { id: 0, first_name: '', last_name: '', img_url: '' }}
                isLoading={addCommentMutation.isPending}
            />

            {/* Comments list */}
            <ScrollArea
                className={`pr-2 ${comments?.length >= 5 ? 'h-130' : heightConfig[comments?.length]} overflow-y-auto w-full`}>
                {comments?.map((comment: CommentType) => (
                    <div key={comment.id} className="w-full max-w-full">
                        <CommentItem
                            comment={comment}
                            onUpvote={handleUpvote}
                            onReply={() => setReplyingTo(comment.id)}
                            setEditingItem={setEditingItem}
                            isEditing={editingItem?.id === comment.id}
                            onAfterDeleteSuccess={onAfterDeleteSuccess}
                            editComment={editCommentMutation.mutate}
                        />

                        {/* Reply input */}
                        {replyingTo === comment.id && (
                            <div className="pl-20 mb-4 text-right">
                                <p className="text-xs text-[#1E1E1E99] cursor-pointer pr-4 pb-2" onClick={handleCancelReply}>
                                    Cancel
                                </p>
                                <CommentInput
                                    value={replyContent}
                                    onChange={setReplyContent}
                                    onSubmit={() => handleAddReply(comment.id)}
                                    user={user || { id: 0, first_name: '', last_name: '', img_url: '' }}
                                    isLoading={addCommentMutation.isPending}
                                    placeholder="Write an Idea ..."
                                    isReply
                                />
                            </div>
                        )}

                        {/* Replies */}
                        <div className="pl-20 w-full ">
                            {comment?.replies?.map((reply, index) => (
                                <div key={reply.id} className="w-full ">
                                    <ReplyItem
                                        key={reply.id}
                                        comment={reply}
                                        onUpvote={handleUpvote}
                                        setEditingItem={setEditingItem}
                                        isEditing={editingItem?.id === reply.id}
                                        onAfterDeleteSuccess={onAfterDeleteSuccess}
                                        editComment={editCommentMutation.mutate}
                                    />
                                    {index < comment.replies.length - 1 && <Separator className="ml-2" />}
                                </div>
                            ))}
                        </div>
                    </div>
                ))}
            </ScrollArea>
        </div>
    )
}

export default Comments
