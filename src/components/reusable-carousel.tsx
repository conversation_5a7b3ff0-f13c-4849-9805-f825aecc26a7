'use client'

import { useEffect, useState } from 'react'
import {
    Carousel,
    CarouselContent,
    CarouselItem,
    CarouselNext,
    CarouselPrevious,
    type CarouselApi,
} from '@/components/ui/carousel'

interface ReusableCarouselProps {
    items: { id: number; component: React.ReactNode }[]
    carouselItemClassName?: string
    carouselContainerClassName?: string
}

export default function ReusableCarousel({
    items,
    carouselItemClassName = 'lg:basis-1/2 mx-auto',
    carouselContainerClassName = 'max-w-[320px] md:max-w-[440px] mx-auto',
}: ReusableCarouselProps) {
    const [api, setApi] = useState<CarouselApi>()
    const [canScrollNext, setCanScrollNext] = useState(false)
    const [canScrollPrev, setCanScrollPrev] = useState(false)

    useEffect(() => {
        if (!api) return

        // Initial state
        setCanScrollNext(api.canScrollNext())
        setCanScrollPrev(api.canScrollPrev())

        api.on('select', () => {
            setCanScrollNext(api.canScrollNext())
            setCanScrollPrev(api.canScrollPrev())
        })
    }, [api])

    return (
        <div className={carouselContainerClassName}>
            <Carousel
                setApi={setApi}
                className="my-2"
                opts={{
                    align: 'center',
                }}>
                {/* Items */}
                <CarouselContent className="mb-2 w-full gap-4">
                    {items.map((item) => (
                        <CarouselItem key={item.id} className={carouselItemClassName}>
                            {item.component}
                        </CarouselItem>
                    ))}
                </CarouselContent>

                <div className="flex gap-2 w-fit mx-auto justify-center pt-8 relative mt-2">
                    <CarouselPrevious
                        className={` h-[38px] w-[38px] bg-black text-white ${
                            !canScrollPrev ? 'opacity-50 cursor-not-allowed bg-white text-black' : ''
                        }`}
                        disabled={!canScrollPrev}
                    />
                    <CarouselNext
                        className={` h-[38px] w-[38px] bg-black text-white ${
                            !canScrollNext ? 'opacity-50 cursor-not-allowed bg-white text-black' : ''
                        }`}
                        disabled={!canScrollNext}
                    />
                </div>
            </Carousel>
        </div>
    )
}
