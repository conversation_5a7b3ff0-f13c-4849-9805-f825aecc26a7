'use client'

import type React from 'react'

import { useEffect, useRef, useState } from 'react'
import { createPortal } from 'react-dom'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface CustomModalProps {
    isOpen: boolean
    onClose: () => void
    children: React.ReactNode
    className?: string
    showCloseButton?: boolean
}

export function CustomModal({ isOpen, onClose, children, className, showCloseButton = true }: CustomModalProps) {
    const [isMounted, setIsMounted] = useState(false)
    const [isAnimating, setIsAnimating] = useState(false)
    const overlayRef = useRef<HTMLDivElement>(null)
    const modalRef = useRef<HTMLDivElement>(null)

    // Handle mounting on client side only
    useEffect(() => {
        setIsMounted(true)
        return () => setIsMounted(false)
    }, [])

    // Handle animation when opening/closing
    useEffect(() => {
        if (isMounted) {
            if (isOpen) {
                setIsAnimating(true)
                document.body.style.overflow = 'hidden' // Prevent scrolling when modal is open
            } else {
                const timer = setTimeout(() => {
                    setIsAnimating(false)
                }, 300) // Match this with CSS transition duration
                document.body.style.overflow = '' // Restore scrolling when modal is closed
                return () => clearTimeout(timer)
            }
        }
    }, [isOpen, isMounted])

    // Handle escape key press
    useEffect(() => {
        const handleEscape = (e: KeyboardEvent) => {
            if (e.key === 'Escape' && isOpen) {
                onClose()
            }
        }

        if (isOpen) {
            document.addEventListener('keydown', handleEscape)
        }

        return () => {
            document.removeEventListener('keydown', handleEscape)
        }
    }, [isOpen, onClose])

    // Handle click outside
    const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
        if (e.target === overlayRef.current) {
            onClose()
        }
    }

    // Don't render anything on the server
    if (!isMounted) return null

    // Don't render if not open and not animating
    if (!isOpen && !isAnimating) return null

    return createPortal(
        <div
            ref={overlayRef}
            onClick={handleOverlayClick}
            className={cn(
                'fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4 backdrop-blur-sm transition-opacity duration-300',
                isOpen ? 'opacity-100' : 'opacity-0',
                !isOpen && 'pointer-events-none',
            )}
            aria-modal="true"
            role="dialog">
            <div
                ref={modalRef}
                className={cn(
                    'bg-white rounded-lg shadow-lg w-full transition-all duration-300',
                    isOpen ? 'scale-100 opacity-100' : 'scale-95 opacity-0',
                    className,
                )}>
                {showCloseButton && (
                    <button
                        onClick={onClose}
                        className="absolute top-4 right-4 p-1 rounded-full hover:bg-gray-100 transition-colors z-50 cursor-pointer"
                        aria-label="Close modal">
                        <X className="h-5 w-5" />
                    </button>
                )}
                {children}
            </div>
        </div>,
        document.body,
    )
}

interface CustomModalTriggerProps {
    onClick: () => void
    children: React.ReactNode
    className?: string
}

export function CustomModalTrigger({ onClick, children, className }: CustomModalTriggerProps) {
    return (
        <div className={className} onClick={onClick}>
            {children}
        </div>
    )
}
