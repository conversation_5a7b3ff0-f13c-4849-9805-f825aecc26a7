'use client'

import React, { useState, FC, useRef } from 'react'
import type { Message } from '@/types/project'
import { api } from '@/config/axios-config'
import { Checkbox } from '../ui/checkbox'
import { CalendarClock, FolderOpenDot, Loader, Trash2 } from 'lucide-react'
import { ScrollArea } from '../ui/scroll-area'
import { DebouncedSearch } from '../debounced-search'
import { useQuery } from '@tanstack/react-query'
import { useProjectStore } from '@/store/project.store'
import { useChatHistory } from '@/hooks/use-chat-history'
import { useAuthStore } from '@/store/auth.store'
import { DeleteWithAlert } from '../delete-with-alert-dialog'
import { toast } from 'sonner'
import { useGlobalState } from '@/store/global-states.store'

interface HistoryTabProps {
    userId: string
    onSelectSession: (sessionId: string, messages: Message[]) => void
}

export const HistoryTab: FC<HistoryTabProps> = ({ userId, onSelectSession }) => {
    const [cache, setCache] = useState<Record<string, Message[]>>({})
    const [checkedSessions, setCheckedSessions] = useState<string[]>([])
    const [searchQuery, setSearchQuery] = useState('')
    const searchRef = useRef<HTMLInputElement>(null)
    const { setSelectedProject } = useProjectStore()
    const { currentWorkspace } = useAuthStore()
    const { activeSession, setActiveSession } = useGlobalState()

    const { getUserId } = useAuthStore()
    const { loadHistory: fetchSessionMessages } = useChatHistory(String(getUserId()))

    const setFocusForTextarea = () => {
        setTimeout(() => {
            searchRef.current?.focus()
        }, 100)
    }

    const loadSessions = async () => {
        try {
            const res = await api.get(`/sessions`, {
                params: { userId, searchQuery: searchQuery, workspaceId: currentWorkspace?.id },
            })
            if (searchQuery) {
                setFocusForTextarea()
            }
            return res.data.sessions || []
        } catch (error) {
            console.error('Failed to load sessions:', error)
        }
    }

    const {
        data: sessions,
        isLoading,
        refetch,
    } = useQuery({
        queryKey: ['sessions', userId, searchQuery, currentWorkspace?.id],
        queryFn: loadSessions,
        enabled: !!userId && !!currentWorkspace?.id,
    })

    const handleSessionClick = async (sessionId: string, projectId: number, projectName: string) => {
        setActiveSession(sessionId)
        const newProject = { id: projectId, name: projectName }
        setSelectedProject(newProject)
        if (cache[sessionId]) {
            onSelectSession(sessionId, cache[sessionId])
        } else {
            const messages = await fetchSessionMessages(sessionId)
            setCache((prev) => ({ ...prev, [sessionId]: messages }))
            onSelectSession(sessionId, messages)
        }
    }

    const handleCheckboxChange = (sessionId: string) => {
        setCheckedSessions((prev) => {
            if (prev.includes(sessionId)) {
                return prev.filter((id) => id !== sessionId)
            } else {
                return [...prev, sessionId]
            }
        })
    }

    const handleClearChecked = () => {
        setCheckedSessions([])
    }

    const clearStates = () => {
        setCheckedSessions([])
        setSearchQuery('')
    }

    const afterMultiDeleteSuccess = () => {
        if (activeSession && checkedSessions.includes(activeSession)) {
            onSelectSession('', [])
        }
        toast.success('Sessions deleted successfully')
        clearStates()
        refetch()
    }

    return (
        <div className="h-full w-full">
            <p className="text-[18px] font-semibold text-[#020617]">
                History<span className="pl-2">({sessions?.length})</span>
            </p>
            <div className="flex my-4">
                <DebouncedSearch
                    onSearchEnd={(value) => setSearchQuery(value)}
                    className="w-full h-[36px]"
                    ref={searchRef as React.Ref<HTMLInputElement> | null}
                />
            </div>
            <div className="flex px-2">
                <p className="font-medium text-xs text-[#666F8D]">Recents</p>
                {checkedSessions.length > 0 && (
                    <div className="ml-auto flex gap-2">
                        <button className="text-[#666F8D] text-xs font-medium cursor-pointer" onClick={handleClearChecked}>
                            Clear
                        </button>
                        <DeleteWithAlert
                            title="Are you sure you want to delete the selected sessions?"
                            description="This action cannot be undone."
                            endpoint={`/sessions/${checkedSessions}`}
                            onAfterSuccess={afterMultiDeleteSuccess}>
                            <Trash2 size={14} color="#9B9B9B" />
                        </DeleteWithAlert>
                    </div>
                )}
            </div>
            <ScrollArea className="w-full h-[70dvh]">
                <>
                    {isLoading ? (
                        <div className="flex items-center justify-center h-120 w-full">
                            <Loader className="h-6 w-6 animate-spin text-gray-400" />
                        </div>
                    ) : sessions?.length === 0 ? (
                        <div className="flex items-center justify-center h-120 w-full">
                            <p className="text-sm text-gray-500">No sessions found</p>
                        </div>
                    ) : (
                        <div className="flex flex-col gap-2 p-2">
                            {sessions?.map(
                                (s: {
                                    sessionId: string
                                    title: string
                                    projectName: string
                                    projectId: number
                                    updatedAt: string
                                }) => (
                                    <SessionItem
                                        key={s.sessionId}
                                        sessionId={s.sessionId}
                                        title={s.title}
                                        projectName={s.projectName}
                                        projectId={s.projectId}
                                        updatedAt={s.updatedAt}
                                        handleSessionClick={handleSessionClick}
                                        handleCheckboxChange={handleCheckboxChange}
                                        isChecked={checkedSessions.includes(s.sessionId)}
                                        activeSession={activeSession}
                                        refetchSessions={() => {
                                            refetch()
                                            clearStates()
                                        }}
                                        onSelectSession={onSelectSession}
                                    />
                                ),
                            )}
                        </div>
                    )}
                </>
            </ScrollArea>
        </div>
    )
}

const SessionItem = ({
    sessionId,
    isChecked,
    title,
    projectId,
    projectName,
    updatedAt,
    handleSessionClick,
    handleCheckboxChange,
    activeSession,
    refetchSessions,
    onSelectSession,
}: {
    sessionId: string
    isChecked: boolean
    title: string
    projectId: number
    projectName: string
    updatedAt: string
    handleSessionClick: (sessionId: string, projectId: number, projectName: string) => void
    handleCheckboxChange: (sessionId: string) => void
    activeSession: string | null
    refetchSessions: () => void
    onSelectSession: (sessionId: string, messages: Message[]) => void
}) => {
    const afterDeleteSuccess = () => {
        if (activeSession === sessionId) {
            onSelectSession('', [])
        }
        toast.success('Session deleted successfully')
        refetchSessions()
    }

    return (
        <div
            className={`flex items-center border-b border-[#EAEAEA] justify-between gap-2 cursor-pointer px-4 hover:bg-gray-100 rounded-lg ${
                sessionId === activeSession ? 'bg-gray-200 font-semibold' : ''
            }`}>
            <div className="flex items-start gap-2 w-full">
                <Checkbox
                    checked={isChecked}
                    onCheckedChange={() => handleCheckboxChange(sessionId)}
                    className="mt-5 cursor-pointer"
                />
                <div className="w-full py-4 " onClick={() => handleSessionClick(sessionId, projectId, projectName)}>
                    <div className="font-medium text-[16px]">{title}</div>
                    <div className="text-xs text-[#9C9C9C] mt-2">
                        <span className="inline-flex">
                            <FolderOpenDot size={14} color="#9C9C9C" className="mr-2" /> {projectName}
                        </span>

                        <span className="inline-flex ml-2">
                            <CalendarClock size={14} color="#9C9C9C" className="mr-2" /> {updatedAt}
                        </span>
                    </div>
                </div>
            </div>
            <DeleteWithAlert
                title="Are you sure you want to delete this session?"
                description="This action cannot be undone."
                endpoint={`/sessions/${sessionId}`}
                onAfterSuccess={afterDeleteSuccess}>
                <div className="p-2 border border-[#CB70702B] rounded-[6px]">
                    <Trash2 size={14} color="#CB7070" />
                </div>
            </DeleteWithAlert>
        </div>
    )
}
