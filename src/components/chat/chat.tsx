import { useRef, useEffect } from 'react'
import { Message } from '@/types/project'
// import { MessageBubble } from './message-bubble'
import { ChatInput } from './chat-input'
import { ZapIcon } from 'lucide-react'

export interface ChatProps {
    messages: Message[]
    onSendMessage: (message: string) => void
    isLoading?: boolean
    onViewToggle?: (view: 'chat' | 'project') => void
    currentView?: 'chat' | 'project'
}

export function Chat({ messages, onSendMessage, isLoading, onViewToggle, currentView }: ChatProps) {
    const messagesEndRef = useRef<HTMLDivElement>(null)

    // Auto-scroll to the bottom when messages change
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
    }

    useEffect(() => {
        scrollToBottom()
    }, [messages])

    return (
        <div className="flex flex-col h-full w-full relative">
            {/* Messages container */}
            {/* <div id="chat-messages-container" className="flex-1 overflow-y-auto px-4 pb-50">
                {messages.map((message) => (
                    <MessageBubble key={message.id} message={message} />
                ))}
                <div ref={messagesEndRef} />
            </div> */}

            {/* Input area - Fixed at bottom */}
            <div className="fixed bottom-0 left-0 right-0 pb-4 px-4 z-5 bg-gradient-to-t from-white dark:from-gray-900 to-transparent pt-6">
                <div className="max-w-4xl mx-auto">
                    <ChatInput
                        onSend={onSendMessage}
                        isLoading={isLoading}
                        onViewToggle={onViewToggle}
                        currentView={currentView}
                    />
                    <div className="mt-2 flex items-center text-xs px-4 text-gray-500 dark:text-gray-400">
                        <ZapIcon className="h-4 w-4 mr-2" />
                        <span>250/250 Prompts</span>
                    </div>
                </div>
            </div>
        </div>
    )
}
