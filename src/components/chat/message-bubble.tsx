'use client'

import { <PERSON><PERSON>, <PERSON><PERSON>sisVertical, RotateCw, Search, TrainTrack } from 'lucide-react'
import { Message } from '@/types/project'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import Image from 'next/image'
import { useAuthStore } from '@/store/auth.store'
import { AssigneeAvatar } from '../assignee-avatar-with-fallback'
import { cn } from '@/lib/utils'
import MarkdownRenderer from '../markdown-renderer'
import { useProjectStore } from '@/store/project.store'
import { useAIAgent } from '@/hooks/use-ai-agent'

interface MessageBubbleProps {
    message: Message
    refetchUserCredits: () => void
    generateTasks: () => void
}

// Constants
const LOADING_INDICATORS = {
    SEARCHING: '🔍 Searching',
    ANALYZING: '✨ Analyzing',
    GENERATING_TASKS: 'Generating detailed project tasks',
} as const

const LOADING_CONTENT_MAP = {
    search: { text: 'Searching the web', icon: <Search className="h-3 w-3 text-[#666F8D]" /> },
    tasks: { text: 'Generating tasks', icon: <TrainTrack className="h-3 w-3 text-[#666F8D]" /> },
    default: { text: 'Thinking', icon: null },
} as const

// Utility functions
const contentIncludesString = (content: string | string[] | object[], searchString: string): boolean => {
    return typeof content === 'string' && content.includes(searchString)
}

const hasSubstantialContent = (content: string): boolean => {
    return Boolean(
        content && content.length > 50 && !Object.values(LOADING_INDICATORS).some((indicator) => content.includes(indicator)),
    )
}

const isStatusMessage = (message: Message): boolean => {
    const parts = message.parts || []
    return (
        parts.length > 0 &&
        parts.every((part) => part.type === 'status') &&
        (message.content.includes(LOADING_INDICATORS.SEARCHING) || message.content.includes(LOADING_INDICATORS.ANALYZING))
    )
}

const isSearchingWeb = (message: Message): boolean => {
    if (hasSubstantialContent(message.content)) return false

    return (
        isStatusMessage(message) || message.content === '🔍 Searching the web...' || message.content === '✨ Analyzing results...'
    )
}

const isGeneratingTasks = (message: Message): boolean => {
    return message.content.includes(LOADING_INDICATORS.GENERATING_TASKS)
}

const isLoading = (message: Message): boolean => {
    if (hasSubstantialContent(message.content)) return false

    return isGeneratingTasks(message) || isSearchingWeb(message) || !message.content
}

const getLoadingContent = (message: Message) => {
    if (isSearchingWeb(message)) return LOADING_CONTENT_MAP.search
    if (isGeneratingTasks(message)) return LOADING_CONTENT_MAP.tasks
    return LOADING_CONTENT_MAP.default
}

const hasSearchResults = (message: Message): boolean => {
    return (message.parts || []).some((part) => part.type === 'status' && contentIncludesString(part.content, 'Searching'))
}

// Components
const Avatar = ({ isUser }: { isUser: boolean }) => {
    const { user } = useAuthStore()

    const avatarContent = isUser ? (
        <AssigneeAvatar
            assignee={user?.first_name + ' ' + user?.last_name}
            imageUrl={user?.img_url}
            className="h-[28px] w-[28px] bg-black"
            fallbackClassName="bg-black text-white p-2 text-xs text-center"
        />
    ) : (
        <Image src={'/assets/img/raydian-logo.png'} width={21} height={21} alt="Scratchpad bot" className="h-auto w-auto" />
    )

    return (
        <div className="flex justify-center items-center h-[24px] min-w-[24px] p-[3px] bg-[#F6FAFF] rounded-full">
            {avatarContent}
        </div>
    )
}

const UserMessage = ({ content }: { content: string }) => (
    <div className="flex items-center flex-row gap-4 w-full">
        <Avatar isUser={true} />
        <div className="flex flex-col bg-[#F3F4F6] rounded-[16px] p-2">
            <p className="text-[16px]">{content}</p>
        </div>
    </div>
)

const LoadingMessage = ({ content }: { content: { text: string; icon: React.ReactNode } }) => (
    <div className="animate-bounce [animation-delay:-0.4s] flex flex-col rounded-[16px] w-full bg-white p-2 border border-[#E5E7EB] shadow-sm shadow-[#28A74517]">
        <Loading text={content.text} icon={content.icon} />
    </div>
)

const ResultBadge = ({ isSearchResult }: { isSearchResult: boolean }) => (
    <div className="flex gap-2 items-center bg-[#9D9D9D12] p-1 rounded-[7px] w-fit mb-2">
        {isSearchResult ? <Search className="h-4 w-4 text-[#666F8D]" /> : <TrainTrack className="h-4 w-4 text-[#666F8D]" />}
        <span className="text-[#666F8D] text-[12px]">{isSearchResult ? 'Search Result' : 'Result'}</span>
    </div>
)

const MessageContent = ({
    message,
    isLoading: loading,
    onGenerateClick,
}: {
    message: Message
    isLoading: boolean
    onGenerateClick: (message: Message) => void
}) => (
    <div className="flex flex-col rounded-[16px] w-full bg-white p-4 border border-[#E5E7EB] shadow-sm shadow-[#28A74517]">
        <ResultBadge isSearchResult={hasSearchResults(message)} />

        {(message.parts || []).map((part, index) => (
            <div key={index} className="max-w-2xl">
                {part.type === 'text' && (
                    <MarkdownRenderer
                        enableExpansion={false}
                        allowCopy={false}
                        content={typeof part.content === 'string' ? part.content : JSON.stringify(part.content)}
                    />
                )}

                {part.type === 'status' && loading && (
                    <div className="flex items-center gap-2 text-sm text-[#666F8D] my-2">
                        {contentIncludesString(part.content, 'Searching') && <Search className="h-4 w-4" />}
                        <span>{typeof part.content === 'string' ? part.content : JSON.stringify(part.content)}</span>
                    </div>
                )}

                {part.type === 'chips' && Array.isArray(part.content) && (
                    <div className="flex flex-wrap gap-2 my-2">
                        {part.content.map((chip, chipIndex) => (
                            <div key={chipIndex} className="px-2 py-0.5 bg-gray-50 text-gray-600 rounded text-xs border">
                                {typeof chip === 'object' ? JSON.stringify(chip) : String(chip)}
                            </div>
                        ))}
                    </div>
                )}

                {part.type === 'generate' && (
                    <Button
                        variant="outline"
                        className="my-4 bg-[#060606] hover:bg-[#060606] transition-shadow hover:text-gray-50 text-gray-50"
                        onClick={() => onGenerateClick(message)}>
                        Generate Tasks
                    </Button>
                )}
            </div>
        ))}
    </div>
)

const ActionButtons = ({
    message,
    onResend,
    onCopy,
}: {
    message: Message
    onResend: () => void
    onCopy: (content: string) => void
}) => (
    <div className="pb-0 flex flex-row justify-between mt-2 ml-14">
        <div className="flex flex-row justify-between items-center gap-2">
            <button className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700" onClick={onResend}>
                <RotateCw size={14} />
            </button>
            <button className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700" onClick={() => onCopy(message.content)}>
                <Copy size={14} />
            </button>
            <button className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700">
                <EllipsisVertical size={14} />
            </button>
        </div>
    </div>
)

const AIMessage = ({
    message,
    refetchUserCredits,
    generateTasks,
}: {
    message: Message
    refetchUserCredits: () => void
    generateTasks: () => void
}) => {
    const { setFeatureSummary, messages } = useProjectStore()
    const { sendMessage, removeMessages } = useAIAgent()

    const loading = isLoading(message)
    const loadingContent = getLoadingContent(message)

    const handleGenerateClick = async (message: Message) => {
        const parts = message.parts || []
        const textParts = parts
            .filter((part) => part.type === 'text' || part.type === 'chips')
            .map((part) => {
                if (part.type === 'text') {
                    return typeof part.content === 'string' ? part.content : JSON.stringify(part.content)
                } else if (part.type === 'chips' && Array.isArray(part.content)) {
                    return part.content.map((chip) => (typeof chip === 'object' ? JSON.stringify(chip) : chip)).join(', ')
                }
                return ''
            })
            .join(' ')

        setFeatureSummary(textParts)
        await generateTasks()
        refetchUserCredits()
    }

    const handleResend = async () => {
        const clickedMessage = messages.findIndex((msg) => msg.id === message.id)
        const userQueryMessage = messages[clickedMessage - 1]
        const allIndexesAfterClicked = messages.slice(clickedMessage + 1)
        const allIds = allIndexesAfterClicked.map((msg) => msg.id)
        const idsTobeRemoved = [userQueryMessage.id, message.id, ...allIds]
        const messagesToBePreserved = messages.filter((msg) => !idsTobeRemoved.includes(msg.id))

        removeMessages([userQueryMessage.id, message.id, ...allIds])
        await sendMessage(userQueryMessage.content, true, messagesToBePreserved)
        refetchUserCredits()
    }

    const handleCopy = (text: string) => {
        navigator.clipboard.writeText(text)
        toast.success('Copied to clipboard!')
    }

    return (
        <div className={cn(loading ? 'w-fit' : 'w-full')}>
            <div className="flex flex-row gap-4">
                <Avatar isUser={false} />
                <div className="w-full">
                    {loading ? (
                        <LoadingMessage content={loadingContent} />
                    ) : (
                        <MessageContent message={message} isLoading={loading} onGenerateClick={handleGenerateClick} />
                    )}
                </div>
            </div>
            <ActionButtons message={message} onResend={handleResend} onCopy={handleCopy} />
        </div>
    )
}

// Main Component
export function MessageBubble({ message, refetchUserCredits, generateTasks }: MessageBubbleProps) {
    const isUser = message.sender === 'user'
    const cleanedMessage = message.content

    return (
        <div className={`mb-2 flex ${isUser ? 'justify-end' : 'justify-start'}`}>
            <div
                className={`rounded-2xl py-1  ${
                    isUser ? 'text-[#616473] max-w-[80%]' : 'text-gray-800 dark:text-gray-200 w-full'
                }`}>
                {isUser ? (
                    <UserMessage content={cleanedMessage} />
                ) : (
                    <AIMessage message={message} refetchUserCredits={refetchUserCredits} generateTasks={generateTasks} />
                )}
            </div>
        </div>
    )
}

export default MessageBubble

// Utility Components
export const Loading = ({
    className,
    text = 'Thinking',
    icon = null,
}: {
    className?: string
    text?: string
    icon?: React.ReactNode
}) => {
    return (
        <div className={cn('flex items-center gap-2 justify-center', className)}>
            {icon && <span>{icon}</span>}
            <div className="w-fit h-full animate-pulse [animation-delay:-0.3s] text-xs">{text}</div>
        </div>
    )
}

export const LoadingDots = ({ className }: { className?: string }) => {
    return (
        <div className={cn('flex items-center gap-1 justify-center', className)}>
            <div className="w-1.5 h-1.5 bg-muted-foreground rounded-full animate-bounce [animation-delay:-0.3s]"></div>
            <div className="w-1.5 h-1.5 bg-muted-foreground rounded-full animate-bounce [animation-delay:-0.15s]"></div>
            <div className="w-1.5 h-1.5 bg-muted-foreground rounded-full animate-bounce"></div>
        </div>
    )
}
