import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'

interface SuggestionCardsProps {
    suggestions: string[]
    onSuggestionClick: (suggestion: string) => void
}

const ANIMATION_VARIANTS = {
    container: {
        hidden: { opacity: 0 },
        show: {
            opacity: 1,
            transition: { staggerChildren: 0.1 },
        },
    },
    item: {
        hidden: { y: 20, opacity: 0 },
        show: { y: 0, opacity: 1 },
    },
}

export const SuggestionCards = ({ suggestions, onSuggestionClick }: SuggestionCardsProps) => {
    return (
        <motion.div
            variants={ANIMATION_VARIANTS.container}
            initial="hidden"
            animate="show"
            className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-6">
            {suggestions.map((text, index) => (
                <motion.div key={index} variants={ANIMATION_VARIANTS.item}>
                    <Card
                        className="h-full hover:shadow-md transition-shadow cursor-pointer"
                        onClick={() => onSuggestionClick(text)}>
                        <CardContent className="py-2 px-3 flex items-center justify-center h-full">
                            <p className="text-gray-500 text-sm">{text}</p>
                        </CardContent>
                    </Card>
                </motion.div>
            ))}
        </motion.div>
    )
}
