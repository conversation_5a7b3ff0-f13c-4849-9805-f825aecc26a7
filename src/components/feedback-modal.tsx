'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { Paperclip, ShieldQuestion } from 'lucide-react'
import { cn } from '@/lib/utils'
import { uploadFile } from '@/lib/minio-client'
import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import { AxiosError } from 'axios'
import endpoints from '@/services/api-endpoints'
import { api } from '@/config/axios-config'
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem } from './ui/sidebar'

// Zod schema
const feedbackSchema = z.object({
    rating: z.number({ required_error: 'Please select a rating' }),
    feedback: z.string().optional(),
})

type FileUploadResult = {
    success: boolean
    fileUrl?: string | null
}

type FeedbackFormSchema = z.infer<typeof feedbackSchema>

const ratingOptions = [
    {
        emoji: '😠',
        label: "Didn't meet expectations",
        value: 1,
        className: 'rounded-[16px] bg-[#55BB6D1C]',
        ring: ' ring-2 ring-red-400',
    },
    { emoji: '😕', label: '', value: 2, className: 'rounded-[16px] bg-[#55BB6D1C]', ring: ' ring-2 ring-red-200' },
    {
        emoji: '😐',
        label: 'Met expectations',
        value: 3,
        className: 'rounded-[16px] bg-[#55BB6D1C]',
        ring: ' ring-2 ring-orange-300',
    },
    { emoji: '🙂', label: '', value: 4, className: 'rounded-[16px] bg-[#55BB6D1C]', ring: ' ring-2 ring-green-300' },
    {
        emoji: '😄',
        label: 'Exceeded expectations',
        value: 5,
        className: 'rounded-[16px] bg-linear-to-r from-[#08B38B] to-[#FFCD29]',
        ring: 'ring-2 ring-green-500',
    },
]

export default function FeedbackModal() {
    const [isOpen, setIsOpen] = useState(false)
    const [imageFile, setImageFile] = useState<File | null>(null)

    const {
        register,
        handleSubmit,
        setValue,
        reset,
        watch,
        formState: { errors },
    } = useForm<FeedbackFormSchema>({
        resolver: zodResolver(feedbackSchema),
        defaultValues: {
            rating: undefined,
            feedback: '',
        },
    })

    const selectedRating = watch('rating')

    const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            setImageFile(e.target.files[0])
        }
    }

    const mutateFeedbackFunction = async (data: FeedbackFormSchema) => {
        try {
            let body = {
                rating: data.rating,
                feedback: data.feedback,
            } as { rating: number; feedback: string; img_url?: string | null }

            let result: FileUploadResult = { success: false, fileUrl: '' }
            if (imageFile) {
                const formData = new FormData()
                formData.append('file', imageFile)
                result = await uploadFile(formData)
            }

            if (imageFile && !result.success) {
                throw new Error('Upload failed')
            }

            if (result.fileUrl) {
                body = { ...body, img_url: result.fileUrl }
            }

            const response = await api.post(endpoints.feedback.submitFeedback, body)
            return response.data
        } catch (err) {
            throw err
        }
    }

    const feedbackMutation = useMutation({
        mutationFn: mutateFeedbackFunction,
        onSuccess: () => {
            toast.success('Feedback submitted successfully!')
            reset()
            setImageFile(null)
            setIsOpen(false)
        },
        onError: (error: AxiosError) => {
            axiosErrorToast(error, 'Failed to submit feedback. Please try again.')
            setIsOpen(true)
        },
    })

    const handleRatingSelect = (rating: number) => {
        setValue('rating', rating, { shouldValidate: true })
    }

    const handleOpenChange = (open: boolean) => {
        setIsOpen(open)
        if (!open) {
            reset()
            setImageFile(null)
        }
    }

    const onSubmit = (data: FeedbackFormSchema) => {
        feedbackMutation.mutate(data)
    }

    return (
        <SidebarMenu>
            <SidebarMenuItem>
                <Dialog open={isOpen} onOpenChange={handleOpenChange}>
                    <DialogTrigger asChild>
                        <SidebarMenuButton className="cursor-pointer">
                            <ShieldQuestion className="w-4 h-4 " /> <span>feedback</span>
                        </SidebarMenuButton>
                    </DialogTrigger>
                    <DialogContent className="w-full">
                        {/* Header */}
                        <DialogTitle>
                            <div className="text-center mb-6">
                                <h2 className="text-[20px] font-semibold text-[#170F49] mb-2">Rate your experience</h2>
                                <p className="text-sm text-[#6F6C8F] font-normal">
                                    Please take a moment to rate your experience with our service.
                                </p>
                            </div>
                        </DialogTitle>
                        <DialogDescription />
                        {/* Rating Section */}
                        <div className="space-y-2 mb-6 -mt-8">
                            <div className="flex justify-center gap-3 w-full">
                                {ratingOptions.map((option) => (
                                    <div key={option.value} className="text-center">
                                        <button
                                            type="button"
                                            onClick={() => handleRatingSelect(option.value)}
                                            className={cn(
                                                'border w-[82px] flex flex-col items-center text-[30px] p-4 transition-all duration-200 hover:scale-110 hover:bg-gray-100',
                                                option.className,
                                                selectedRating === option.value && option.ring,
                                                errors.rating && 'ring-2 ring-red-500',
                                            )}
                                            aria-label={`Rate ${option.value} out of 5${option.label ? `: ${option.label}` : ''}`}>
                                            {option.emoji}
                                        </button>
                                        {option.label && (
                                            <p className="text-xs text-[#170F49] font-medium mt-1">{option.label}</p>
                                        )}
                                    </div>
                                ))}
                            </div>
                            {errors.rating && <p className="text-sm text-red-500 text-center mt-2">{errors.rating.message}</p>}
                        </div>

                        {/* Contact Support */}
                        <div className="-mb-3">
                            <button
                                type="button"
                                className="text-[13px] text-[#414651] font-medium hover:underline"
                                onClick={() => console.log('Contact support clicked')}>
                                Contact Support
                            </button>
                        </div>

                        {/* Feedback Section */}
                        <div className="space-y-2 mb-6">
                            <div className="relative">
                                <Textarea
                                    placeholder="What feature or fix would make Raydian even better for you?"
                                    className="min-h-40 max-h-50 resize-none border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                                    {...register('feedback')}
                                />
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className={cn(
                                        `text-xs absolute bottom-1 right-1 bg-[#1D1D1D0A] rounded-[11px]`,
                                        imageFile && 'bg-[#26a1479d]',
                                    )}
                                    asChild>
                                    <label>
                                        <input
                                            type="file"
                                            className="hidden"
                                            accept="image/*"
                                            onChange={handleImageUpload} // Handle file selection
                                        />
                                        <Paperclip className="h-4 w-4" />
                                    </label>
                                </Button>
                            </div>
                            {errors.feedback && <p className="text-sm text-red-500">{errors.feedback.message}</p>}
                        </div>

                        {/* Submit Button */}
                        <Button
                            onClick={handleSubmit(onSubmit)}
                            className="w-full bg-gray-800 hover:bg-gray-900 text-white py-3 rounded-[6px]"
                            disabled={!selectedRating}>
                            {feedbackMutation.isPending ? 'Submitting...' : 'Submit'}
                        </Button>
                    </DialogContent>
                </Dialog>
            </SidebarMenuItem>
        </SidebarMenu>
    )
}
