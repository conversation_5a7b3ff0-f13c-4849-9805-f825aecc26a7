'use client'

import { useState } from 'react'
import {
    DndContext,
    DragOverlay,
    closestCorners,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
    type DragStartEvent,
    type DragEndEvent,
    type DragOverEvent,
    type UniqueIdentifier,
} from '@dnd-kit/core'
import { SortableContext, sortableKeyboardCoordinates, horizontalListSortingStrategy } from '@dnd-kit/sortable'
import { useKanbanStore } from '@/store/kanban-dnd-store'
import Column from './column'
import TaskCard from './task-card'
// import AddColumn from "./add-column"
import type { DnDColumnType as ColumnType, DnDTaskType as Task } from './types'
export default function KanbanBoard({ onDragComplete }: { onDragComplete?: () => void }) {
    const { columns, tasks, reorderColumn, reorderTask, moveTaskToColumn } = useKanbanStore()

    const [activeColumn, setActiveColumn] = useState<ColumnType | null>(null)
    const [activeTask, setActiveTask] = useState<Task | null>(null)

    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 8,
            },
        }),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        }),
    )

    function handleDragStart(event: DragStartEvent) {
        const { active } = event
        const activeId = active.id.toString()

        // Check if we're dragging a column
        if (activeId.includes('column')) {
            const columnId = Number.parseInt(activeId.replace('column-', ''))
            const column = columns.find((col) => col.id === columnId)
            if (column) setActiveColumn(column)
            return
        }

        // Otherwise, we're dragging a task
        const taskId = Number.parseInt(activeId)
        const task = tasks.find((t) => t.id === taskId)
        if (task) setActiveTask(task)
    }

    function handleDragOver(event: DragOverEvent) {
        const { active, over } = event
        if (!over) return

        const activeId = active.id.toString()
        const overId = over.id.toString()

        // Skip if hovering over the same item
        if (activeId === overId) return

        // If we're dragging a column, we don't need special handling here
        if (activeId.includes('column')) return

        // We're dragging a task
        const taskId = Number.parseInt(activeId)
        const task = tasks.find((t) => t.id === taskId)
        if (!task) return

        // Case 1: Dropping on a container (empty column or column area)
        if (overId.includes('container')) {
            const targetColumnId = Number.parseInt(overId.replace('container-', ''))
            const sourceColumnId = task.status_id

            // Skip if the task is already in the target column
            if (sourceColumnId === targetColumnId) return

            // Move to the empty column
            moveTaskToColumn(taskId, sourceColumnId, targetColumnId)

            return
        }

        // Case 2: Dropping on another task
        const overTaskId = Number.parseInt(overId)
        const overTask = tasks.find((t) => t.id === overTaskId)

        if (!overTask) return
        if (task.status_id === overTask.status_id) {
            const columnId = task.status_id
            const tasksInColumn = tasks.filter((t) => t.status_id === columnId)

            const oldIndex = tasksInColumn.findIndex((t) => t.id === taskId)
            const newIndex = tasksInColumn.findIndex((t) => t.id === overTaskId)

            if (oldIndex !== newIndex) {
                reorderTask(columnId, oldIndex, newIndex)
            }
        }
        // If tasks are in different columns
        if (task.status_id !== overTask.status_id) {
            const targetColumnId = overTask.status_id
            const sourceColumnId = task.status_id
            const tasksInTargetColumn = tasks.filter((t) => t.status_id === targetColumnId)
            const overTaskIndex = tasksInTargetColumn.findIndex((t) => t.id === overTaskId)

            moveTaskToColumn(taskId, sourceColumnId, targetColumnId, overTaskIndex)
        }
    }

    async function handleDragEnd(event: DragEndEvent) {
        const { active, over } = event

        if (!over) {
            setActiveColumn(null)
            setActiveTask(null)
            return
        }

        const activeId = active.id.toString()
        const overId = over.id.toString()

        // If we're dragging a column
        if (activeId.includes('column') && overId.includes('column')) {
            const activeColumnId = Number.parseInt(activeId.replace('column-', ''))
            const overColumnId = Number.parseInt(overId.replace('column-', ''))

            if (activeColumnId !== overColumnId) {
                const oldIndex = columns.findIndex((col) => col.id === activeColumnId)
                const newIndex = columns.findIndex((col) => col.id === overColumnId)
                reorderColumn(oldIndex, newIndex)
            }
        }
        // If we're dragging a task
        else if (!activeId.includes('column')) {
            const taskId = Number.parseInt(activeId)
            const task = tasks.find((t) => t.id === taskId)

            if (!task) {
                setActiveColumn(null)
                setActiveTask(null)
                return
            }

            // Case 1: Dropping on a container (empty column)
            if (overId.includes('container')) {
                const targetColumnId = Number.parseInt(overId.replace('container-', ''))

                // Only process if the column is different
                if (task.status_id !== targetColumnId) {
                    const sourceColumnId = task.status_id

                    // Make API call
                    moveTaskToColumn(taskId, sourceColumnId, targetColumnId)
                    // Log for debugging
                }
            }
            // Case 2: Dropping on another task
            else if (!overId.includes('column')) {
                const overTaskId = Number.parseInt(overId)
                const overTask = tasks.find((t) => t.id === overTaskId)

                if (overTask) {
                    // If tasks are in the same column
                    if (task.status_id === overTask.status_id) {
                        const columnId = task.status_id
                        const tasksInColumn = tasks.filter((t) => t.status_id === columnId)

                        const oldIndex = tasksInColumn.findIndex((t) => t.id === taskId)
                        const newIndex = tasksInColumn.findIndex((t) => t.id === overTaskId)

                        if (oldIndex !== newIndex) {
                            reorderTask(columnId, oldIndex, newIndex)
                        }
                    }
                    // If tasks are in different columns
                    else {
                        const sourceColumnId = task.status_id
                        const targetColumnId = overTask.status_id
                        const tasksInTargetColumn = tasks.filter((t) => t.status_id === targetColumnId)
                        const overTaskIndex = tasksInTargetColumn.findIndex((t) => t.id === overTaskId)
                        moveTaskToColumn(taskId, sourceColumnId, targetColumnId, overTaskIndex)
                    }
                }
            }
        }
        if (onDragComplete) {
            onDragComplete()
        }
        setActiveColumn(null)
        setActiveTask(null)
    }

    return (
        <DndContext
            sensors={sensors}
            collisionDetection={closestCorners}
            onDragStart={handleDragStart}
            onDragOver={handleDragOver}
            onDragEnd={handleDragEnd}>
            <div className="flex gap-6 justify-center overflow-x-auto pb-4">
                <SortableContext
                    items={columns.map((col) => `column-${col.id}` as UniqueIdentifier)}
                    strategy={horizontalListSortingStrategy}>
                    {columns.map((column) => (
                        <Column key={column.id} column={column} tasks={tasks.filter((task) => task.status_id === column.id)} />
                    ))}
                </SortableContext>

                {/* <AddColumn /> */}
            </div>

            {/* Drag Overlays */}
            <DragOverlay>
                {activeColumn && tasks.filter((task) => task.status_id === activeColumn.id).length > 0 && (
                    <Column column={activeColumn} tasks={tasks.filter((task) => task.status_id === activeColumn.id)} />
                )}

                {activeTask && <TaskCard task={activeTask} isDragging />}
            </DragOverlay>
        </DndContext>
    )
}
