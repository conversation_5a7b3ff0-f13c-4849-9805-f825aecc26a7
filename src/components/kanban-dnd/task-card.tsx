'use client'

import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import type { DnDTaskType as Task } from './types'
import { Bug, Calendar, Clock, Flame, Link2 } from 'lucide-react'
import { AssigneeAvatar } from '../assignee-avatar-with-fallback'
import { formatDate } from '@/utils/format-date.utils'
import { Card, CardContent } from '@/components/ui/card'
import DepartmentChip from '../ui/department-chip'
import { TooltipCard } from './tooltip-card'
import { useRouter } from 'next/navigation'
import getStatusIcon from '../get-status-icon'
import { bookmarkTask } from '@/services/task-bookmark.service'
import Bookmark from '../../../public/assets/icons/bookmarked.svg'
import Image from 'next/image'
interface TaskCardProps {
    task: Task
    isDragging?: boolean
}

type PriorityType = 'high' | 'medium' | 'low' | 'none' | ''

function getPriorityColor(priority: PriorityType): string {
    switch (priority.toLowerCase()) {
        case 'high':
            return '#E35422' // Red
        case 'medium':
            return '#fbbf24' // Yellow
        case 'low':
            return '#34d399' // Green
        default:
            return '#6b7280' // Gray
    }
}

export default function TaskCard({ task, isDragging = false }: TaskCardProps) {
    const router = useRouter()
    const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
        id: task.id,
        data: {
            type: 'task',
            task,
        },
    })

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
    }

    const dueDate = task.due_date ? new Date(task.due_date) : null

    const isPastDue = dueDate && task.isDelayed ? dueDate < new Date() && !isDragging : false

    function handleCardClick() {
        router.push(`task/${task.id}`)
    }
    const handleBookmarkClick = async (e: React.MouseEvent) => {
        e.stopPropagation()
        await bookmarkTask(task.id)
    }

    return (
        <Card
            ref={setNodeRef}
            style={style}
            {...attributes}
            {...listeners}
            className={`max-h-[280px] cursor-grab rounded-[10px] py-2 transition-shadow border-gray-200`}
            onClick={handleCardClick}>
            {
                <div className={`${isPastDue ? 'border-l-2 border-l-[#E83F25]' : 'border-none'}`}>
                    <div className="px-2 pb-2 flex flex-row justify-between items-start border-b-2 border-[#DBDBDB87]">
                        <DepartmentChip shortCode={task.taskDepartment?.short_code || ''} label={task.short_code} size="sm" />
                        <div className="flex gap-2 items-center min-h-5">
                            {task?.parentTasks && task?.parentTasks?.length > 0 && (
                                <TooltipCard
                                    trigger={
                                        <div className="flex bg-[#F8F8F8] justify-center items-center border rounded-md h-[23px] w-[24px]">
                                            <Link2 size={12} color="#000000" />
                                        </div>
                                    }>
                                    <Card className="p-0 rounded-md w-[225px]">
                                        <CardContent className="px-0">
                                            <div className="border-b p-2">
                                                <div className="flex items-center gap-2">
                                                    <div className="flex justify-center bg-[#F8F8F8] items-center border rounded-md h-[23px] w-[24px]">
                                                        <Link2 size={12} color="#000000" />
                                                    </div>
                                                    <p className="text-sm font-medium text-[#09090B]">Parent Task</p>
                                                </div>
                                            </div>
                                            <div className="px-2 my-4">
                                                <div>
                                                    {task?.parentTasks
                                                        ?.slice() // Create a copy to avoid mutating the original
                                                        .sort(
                                                            (a, b) =>
                                                                a.task_dependency_mapping.dependency_level -
                                                                b.task_dependency_mapping.dependency_level,
                                                        )
                                                        .map((parentTask) => (
                                                            <div
                                                                className="w-full mb-1 flex items-center gap-2"
                                                                key={parentTask.id}>
                                                                <DepartmentChip
                                                                    label={parentTask.short_code}
                                                                    shortCode={parentTask.taskDepartment?.short_code || ''}
                                                                    size="sm"
                                                                />
                                                                <div className="flex items-center gap-1">
                                                                    <p>{getStatusIcon(parentTask.taskStatus.label)}</p>
                                                                    <p>{parentTask.taskStatus.label}</p>
                                                                </div>
                                                            </div>
                                                        ))}
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </TooltipCard>
                            )}
                            {task?.taskPriority?.label?.toLowerCase() === 'high' && (
                                <Flame
                                    color={getPriorityColor(task?.taskPriority?.label?.toLowerCase() as PriorityType)}
                                    size={18}
                                />
                            )}
                            {task.bug && <Bug size={12} color="#FF0000" />}
                            {task?.isBookmarked && (
                                <div className="cursor-pointer h-[19px] w-[12px] relative" onClick={handleBookmarkClick}>
                                    <Image src={Bookmark} alt="Bookmark" fill className="object-cover" />
                                </div>
                            )}
                        </div>
                    </div>

                    <CardContent className="py-2">
                        <p className="font-medium text-[15px] mb-2">{task.title}</p>
                        <div className="flex justify-between text-xs text-gray-500">
                            <div className="flex items-center gap-1">
                                <Clock size={12} />
                                <span>Est Time :</span>
                                <span>{task.time_estimate_hrs}hrs</span>
                            </div>
                        </div>

                        <div className="mt-3 flex gap-2 w-full items-center justify-between">
                            <div className="flex gap-2 items-center">
                                <div className="flex -space-x-2">
                                    {task.taskAssignees?.slice(0, 3)?.map((member) => (
                                        <AssigneeAvatar
                                            key={member.id}
                                            assignee={member.first_name + ' ' + member.last_name}
                                            imageUrl={member.img_url}
                                            className="border-2 border-white h-[24px] w-[24px]"
                                        />
                                    ))}
                                    {task.taskAssignees && task.taskAssignees.length > 3 && (
                                        <AssigneeAvatar
                                            className="border-2 border-white h-[24px] w-[24px]"
                                            assignee={`+ ${
                                                task.taskAssignees.length - 3 > 1
                                                    ? task.taskAssignees.length - 3 + 'Others'
                                                    : task.taskAssignees.length - 3 + 'Other'
                                            }`}
                                        />
                                    )}
                                </div>
                            </div>
                            <div className="flex items-center gap-1">
                                {dueDate && <Calendar size={16} color="#9BA5B1" />}
                                <span className={isPastDue ? 'text-[#E83F25] font-xs text-xs' : 'text-xs text-[#9BA5B1]'}>
                                    {dueDate ? formatDate(dueDate, 'ddMonYYYY') : ''}
                                </span>
                            </div>
                        </div>
                    </CardContent>
                </div>
            }
        </Card>
    )
}
