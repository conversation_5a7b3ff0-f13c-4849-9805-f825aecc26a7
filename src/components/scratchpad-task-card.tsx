import { motion } from 'framer-motion'
import { ArrowUpRight, Link2 } from 'lucide-react'
import React from 'react'
import TimeEstimateChip from './ui/time-estimate-chip'
import ChecklistChip from './ui/checklist-chip'
import DepartmentChip from './ui/department-chip'
import { TooltipCard } from './kanban-dnd/tooltip-card'
import { Card } from './ui/card'
import { cn } from '@/lib/utils'

interface TaskCardProps {
    taskNum: number
    id: string
    category: string
    assignedRole: string
    description: string
    estimatedHours: number
    title: string
    dependencies: string[]
    showBottomBorder?: boolean
    acceptanceCriteria: string[]
}

const TaskCard: React.FC<TaskCardProps> = ({
    taskNum,
    id,
    category,
    description,
    estimatedHours,
    title,
    dependencies,
    showBottomBorder = true,
    acceptanceCriteria,
}) => {
    return (
        <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, type: 'spring', stiffness: 100, damping: 12 }}
            className="flex flex-row">
            <div className="flex flex-row ml-[10px] border-l border-zinc-300 relative">
                <div className="absolute -left-[10px] h-[20px] w-[20px] px-[8px] py-[4px] flex justify-center items-center rounded-[6px] bg-zinc-100 border border-[#38383812]">
                    <span className="font-bold text-[#60646C] text-center text-[13px]">{taskNum}</span>
                </div>
                {dependencies && dependencies.length > 0 && (
                    <div className="absolute -left-[10px] top-9 h-[20px] w-[20px] p-[2px] flex justify-center items-center rounded-[6px] bg-slate-200 border border-[#38383812]">
                        <TooltipCard trigger={<Link2 size={12} color="#000000" />}>{dependencyToolTip(dependencies)}</TooltipCard>
                    </div>
                )}
            </div>
            <div className={showBottomBorder ? 'w-full border-b border-[#0000001A] mb-2' : 'w-full'}>
                <div className="pl-[10px] pt-0 p-[15px] mt-0 mx-[13px] ">
                    <div className="flex flex-row items-center justify-between">
                        <div className="flex items-center text-gray-500 text-[12px]">
                            <span className="font-semibold underline">{id}</span>
                            <ArrowUpRight size={14} className="ml-0.5" />
                        </div>
                    </div>
                    <div className="flex items-center gap-2 mt-[13px] mb-[5px] text-gray-600">
                        {/* Department chip */}
                        <DepartmentChip shortCode={category.toLowerCase()} label={category} size="sm" />
                        {/* Estimated hours chip */}
                        <TimeEstimateChip estimate={estimatedHours} size="sm" />
                        {/* Steps chip */}
                        {acceptanceCriteria?.length > 0 && <ChecklistChip steps={acceptanceCriteria.length} size="sm" />}
                    </div>
                    <h3 className="text-md font-semibold py-[6px]">{title}</h3>
                    <p className="text-sm text-[#757575] text-justify leading-[24px]">{description}</p>
                </div>
            </div>
        </motion.div>
    )
}

export default TaskCard

const dependencyToolTip = (dependencies: string[]) => {
    return (
        <Card className="p-4 max-w-fit rounded-[18px]">
            <div className="flex items-center gap-2 border-b pb-2">
                <Link2 size={14} color="#505156" />
                <p className="text-sm font-medium">Task Dependencies</p>
            </div>
            <div className="pl-4">
                {dependencies.map((dependency, index) => (
                    <div
                        key={dependency}
                        className={cn(
                            'relative flex items-center border-dashed border-l-2 pl-3 pb-2 border-[#ADB1C0]',
                            index === dependencies.length - 1 ? 'border-l-transparent' : '',
                        )}>
                        <div>
                            <p className="text-[#ADB1C0] text-[9px]">TASK{index + 1}.</p>
                            <p className="text-[#000000] text-[13px] underline">{dependency}</p>
                        </div>
                        <div className="absolute -left-2 top-0 h-[13px] w-[13px] p-[2px] flex justify-center items-center rounded-full bg-slate-200 border border-[#38383812]" />
                    </div>
                ))}
            </div>
        </Card>
    )
}
