'use client'

import { useEffect } from 'react'
import { cn } from '@/lib/utils'
import { useFilterStore } from '@/store/filter-store'
import type { FilterCategory } from './types'

interface FilterSidebarProps {
    categories: FilterCategory[]
}

export function FilterSidebar({ categories }: FilterSidebarProps) {
    const { activeCategory, setActiveCategory, getTempSelectedCountByCategory, setActiveEndpoint } = useFilterStore()

    // Set the default category on mount if no activeCategory is set
    useEffect(() => {
        if (!activeCategory) {
            const defaultCategory = categories.find((cat) => cat?.isDefault)
            if (defaultCategory) {
                setActiveCategory(defaultCategory.id)
                setActiveEndpoint(defaultCategory.endpoint || '')
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    return (
        <div className="w-full">
            <h2 className="text-lg font-medium pl-4 pt-2">Filters</h2>

            <div className="space-y-1 px-2">
                {categories.map((category) => {
                    const selectedCount = getTempSelectedCountByCategory(category.id)

                    return (
                        <button
                            key={category.id}
                            onClick={() => {
                                setActiveCategory(category.id)
                                setActiveEndpoint(category.endpoint || '')
                            }}
                            className={cn(
                                'rounded-[8px] flex w-full items-center justify-between px-4 py-2 text-sm hover:bg-muted/60 h-[40px]',
                                activeCategory === category.id && 'bg-muted/50',
                            )}>
                            <div className="flex items-center gap-3 justify-between w-full h-full">
                                <div className="flex gap-3 items-center">
                                    {category.icon}
                                    <span>{category.label}</span>
                                </div>
                                <div>
                                    {selectedCount > 0 && (
                                        <div className="px-2 py-1 text-xs bg-[#F5F5F5] rounded-sm">{selectedCount}</div>
                                    )}
                                </div>
                            </div>
                        </button>
                    )
                })}
            </div>
        </div>
    )
}
