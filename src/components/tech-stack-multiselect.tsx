'use client'

import React, { useState, useRef, useCallback } from 'react'
import { CheckIcon, Plus, Search, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover'
import { <PERSON><PERSON> } from './ui/button'
import { ScrollArea } from './ui/scroll-area'
import { Input } from './ui/input'

type Option = {
    id: string
    label: string
    icon?: React.ReactNode
}

interface MultiSelectProps {
    options: Option[]
    selectedValues: string[]
    onChange: (values: string[]) => void
    placeholder?: string
    maxDisplay?: number
    className?: string
    selectControllerWidth?: string
}

export function MultiSelect({
    options,
    selectedValues,
    onChange,
    maxDisplay = 3,
    selectControllerWidth = 'w-[290px]',
}: MultiSelectProps) {
    const [searchTerm, setSearchTerm] = useState('')
    const containerRef = useRef<HTMLDivElement>(null)
    const inputRef = useRef<HTMLInputElement>(null)

    const filteredOptions = options.filter((option) => option.label.toLowerCase().includes(searchTerm.toLowerCase()))
    const selectedOptions = options.filter((option) => selectedValues.includes(option.id))

    const toggleOption = useCallback(
        (optionId: string) => {
            if (selectedValues.includes(optionId)) {
                onChange(selectedValues.filter((id) => id !== optionId))
            } else {
                onChange([...selectedValues, optionId])
            }
        },
        [selectedValues, onChange],
    )

    const removeOption = useCallback(
        (optionId: string, e: React.MouseEvent) => {
            e.stopPropagation()
            onChange(selectedValues.filter((id) => id !== optionId))
        },
        [selectedValues, onChange],
    )

    const visibleTags = selectedOptions.slice(0, maxDisplay)
    const hiddenCount = selectedOptions.length - visibleTags.length

    return (
        <div className="relative w-full" ref={containerRef}>
            <div className="flex flex-row gap-2">
                <div
                    className={cn(
                        'flex flex-row bg-white h-[39px] items-center border border-[#E8E8ED] rounded-[8px] p-[5px]',
                        selectedOptions.length > 0 ? 'pl-2' : '',
                        selectControllerWidth,
                    )}>
                    <div className="flex flex-wrap gap-1 items-center flex-1">
                        {visibleTags.map((option) => (
                            <div
                                key={option.id}
                                className="flex items-center gap-1.5 rounded-[8px] bg-[#F1F5F9] px-[6px] py-[5px]">
                                {option.icon}
                                <span className="text-[12px]">{option.label}</span>
                                <button
                                    type="button"
                                    onClick={(e) => removeOption(option.id, e)}
                                    className="ml-1 rounded-full hover:bg-gray-200 p-0.5">
                                    <X className="h-3 w-3" />
                                    <span className="sr-only">Remove {option.label}</span>
                                </button>
                            </div>
                        ))}
                        {hiddenCount > 0 && (
                            <div className="flex items-center px-2 py-1 text-[12px] float-end">+{hiddenCount}</div>
                        )}
                    </div>
                </div>
                <div className="flex items-center gap-1">
                    <Popover>
                        <PopoverTrigger asChild>
                            <Button variant="outline" className="h-[39px] w-[35px]">
                                <Plus className="h-5 w-5" />
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent align="end" className="w-[335px] py-[4px] px-[8px]">
                            <div className="border-b">
                                <div className="flex items-center gap-2 text-gray-500">
                                    <Search className="w-5 h-5" />
                                    <Input
                                        ref={inputRef}
                                        type="text"
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="w-full text-[12px] py-[8px] border-none outline-none focus-visible:outline-none focus-visible:ring-0 bg-transparent text-gray-700 placeholder-gray-400 focus:outline-none text-base"
                                        placeholder="Type Tech stack or search..."
                                        autoFocus
                                    />
                                </div>
                            </div>
                            <div className="px-[8px] py-[8px] text-[#64748B] text-xs font-medium">Suggestions</div>
                            <div className="max-h-60 overflow-auto">
                                <ScrollArea className="max-h-60">
                                    {filteredOptions.length > 0 ? (
                                        filteredOptions.map((option) => {
                                            const isSelected = selectedValues.includes(option.id)
                                            return (
                                                <div
                                                    key={option.id}
                                                    onClick={() => toggleOption(option.id)}
                                                    className={cn(
                                                        'flex items-center justify-between h-[32px] py-[6px] px-[9px] rounded-[4px] text-[14px] cursor-pointer',
                                                        isSelected ? 'bg-[#F1F5F9]' : 'hover:bg-gray-50',
                                                    )}>
                                                    <div className="flex items-center gap-3">
                                                        <div className="flex-shrink-0 w-5 h-5">{option.icon}</div>
                                                        <span className="font-medium">{option.label}</span>
                                                    </div>
                                                    {isSelected && <CheckIcon className="h-[14px] w-[14px] text-[#0ACF83]" />}
                                                </div>
                                            )
                                        })
                                    ) : (
                                        <div className="py-4 px-[] text-base text-gray-500">No options found</div>
                                    )}
                                </ScrollArea>
                            </div>
                        </PopoverContent>
                    </Popover>
                </div>
            </div>
        </div>
    )
}
