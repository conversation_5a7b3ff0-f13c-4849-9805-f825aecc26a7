'use client'

import { useCallback } from 'react'
import { Bell, Loader, AlertCircle } from 'lucide-react'
import React from 'react'

import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { AssigneeAvatar } from './assignee-avatar-with-fallback'

// Import the custom hook and types
import { useNotifications, type Notification } from '@/hooks/use-realtime-notifications'

// Constants
const MAX_NOTIFICATION_HEIGHT = 'max-h-64'

// Types
interface NotificationBellProps {
    userId?: number
    workspaceId?: number
}

interface NotificationItemProps {
    note: Notification
    onClick: () => void
    isLoading?: boolean
}

// Memoized NotificationItem component
const NotificationItem = React.memo<NotificationItemProps>(({ note, onClick, isLoading = false }) => {
    const handleClick = useCallback(() => {
        if (!isLoading) {
            onClick()
        }
    }, [onClick, isLoading])

    const handleKeyDown = useCallback(
        (event: React.KeyboardEvent) => {
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault()
                handleClick()
            }
        },
        [handleClick],
    )

    const fullName = `${note.actor.first_name} ${note.actor.last_name}`

    return (
        <div
            className={cn(
                'flex p-2 border-b items-start cursor-pointer hover:bg-gray-50 focus-visible:bg-gray-50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500',
                isLoading && 'opacity-50 pointer-events-none',
            )}
            onClick={handleClick}
            onKeyDown={handleKeyDown}
            tabIndex={0}
            role="button"
            aria-label={`Notification from ${fullName}: ${note.message}. ${note.isRead ? 'Read' : 'Unread'}`}>
            <AssigneeAvatar assignee={fullName} imageUrl={note.actor.img_url} className="h-[32px] w-[32px]" />
            <div className="flex-1 ml-3">
                <p className="text-xs text-gray-800">{note.message}</p>
            </div>
            <div className="flex items-center gap-1 text-xs text-[#0000004D]">
                <span>{note.timeAgo}</span>
                {!note.isRead && <div className="w-2 h-2 rounded-full bg-[#F24822]" aria-label="Unread notification indicator" />}
            </div>
        </div>
    )
})

NotificationItem.displayName = 'NotificationItem'

// Main component
export default function NotificationBell({ userId, workspaceId }: NotificationBellProps) {
    const { displayedNotifications, unreadCount, isUnreadTab, setIsUnreadTab, markAsRead, markAllAsRead, isLoading, error } =
        useNotifications({ userId, workspaceId })

    const handleMarkAsRead = useCallback(
        (id: number) => {
            markAsRead.mutate(id)
        },
        [markAsRead],
    )

    const handleMarkAllAsRead = useCallback(() => {
        markAllAsRead.mutate()
    }, [markAllAsRead])

    const handleTabChange = useCallback(
        (isUnread: boolean) => {
            setIsUnreadTab(isUnread)
        },
        [setIsUnreadTab],
    )

    // Reset to unread tab when popover opens
    const handleOpenChange = useCallback(
        (open: boolean) => {
            if (open) {
                setIsUnreadTab(true)
            }
        },
        [setIsUnreadTab],
    )

    if (!userId || !workspaceId) {
        return null
    }

    return (
        <Popover onOpenChange={handleOpenChange}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    size="icon"
                    className="relative"
                    aria-label={`Notifications ${unreadCount > 0 ? `(${unreadCount} unread)` : ''}`}>
                    <Bell className="h-4 w-4" />
                    {unreadCount > 0 && (
                        <span
                            className="absolute -top-2 -right-3 flex items-center justify-center h-5 w-5 text-xs font-bold text-white bg-red-600 rounded-full"
                            aria-label={`${unreadCount} unread notifications`}>
                            {unreadCount > 99 ? '99+' : unreadCount}
                        </span>
                    )}
                </Button>
            </PopoverTrigger>

            <PopoverContent className="w-80 p-0" align="end">
                {/* Header with Tabs */}
                <div className="flex justify-between items-center border-b px-2">
                    <div className="flex gap-2 px-4 py-2" role="tablist">
                        <button
                            className={cn(
                                'text-xs flex gap-1 items-center cursor-pointer transition-colors',
                                isUnreadTab ? 'font-bold text-black' : 'text-gray-500 hover:text-gray-700',
                            )}
                            onClick={() => handleTabChange(true)}
                            role="tab"
                            aria-selected={isUnreadTab}
                            aria-controls="unread-notifications">
                            Unread
                            {unreadCount > 0 && (
                                <span className="text-xs w-[20px] h-[18px] bg-[#FFE2E0] rounded-[6px] text-[#F24822] flex items-center justify-center">
                                    {unreadCount > 99 ? '99+' : unreadCount}
                                </span>
                            )}
                        </button>
                        <button
                            className={cn(
                                'text-xs flex gap-1 items-center cursor-pointer transition-colors',
                                !isUnreadTab ? 'font-bold text-black' : 'text-gray-500 hover:text-gray-700',
                            )}
                            onClick={() => handleTabChange(false)}
                            role="tab"
                            aria-selected={!isUnreadTab}
                            aria-controls="all-notifications">
                            All
                        </button>
                    </div>
                    {isUnreadTab && displayedNotifications.length > 0 && (
                        <button
                            onClick={handleMarkAllAsRead}
                            className="text-xs text-[#28A745] hover:underline mr-3 transition-colors disabled:opacity-50"
                            disabled={markAllAsRead.isPending}
                            aria-label="Mark all notifications as read">
                            {markAllAsRead.isPending ? 'Marking...' : 'Mark all as read'}
                        </button>
                    )}
                </div>

                {/* Content */}
                {error ? (
                    <div className="flex flex-col items-center justify-center p-6 text-center">
                        <AlertCircle className="h-8 w-8 text-red-500 mb-2" />
                        <p className="text-sm text-red-600 mb-3">Failed to load notifications</p>
                        <Button size="sm" variant="outline" onClick={() => window.location.reload()}>
                            Retry
                        </Button>
                    </div>
                ) : isLoading ? (
                    <div className="flex justify-center p-4" role="status" aria-label="Loading notifications">
                        <Loader className="animate-spin" size={15} />
                    </div>
                ) : (
                    <div
                        className={`py-2 ${MAX_NOTIFICATION_HEIGHT} overflow-auto`}
                        role="tabpanel"
                        id={isUnreadTab ? 'unread-notifications' : 'all-notifications'}>
                        {displayedNotifications.length === 0 ? (
                            <p className="px-4 py-8 text-center text-sm text-gray-500">
                                {isUnreadTab ? 'No unread notifications' : 'No notifications'}
                            </p>
                        ) : (
                            <div role="list" aria-label={isUnreadTab ? 'Unread notifications' : 'All notifications'}>
                                {displayedNotifications.map((note) => (
                                    <NotificationItem
                                        key={note.id}
                                        note={note}
                                        onClick={() => handleMarkAsRead(note.id)}
                                        isLoading={markAsRead.isPending}
                                    />
                                ))}
                            </div>
                        )}
                    </div>
                )}
            </PopoverContent>
        </Popover>
    )
}
