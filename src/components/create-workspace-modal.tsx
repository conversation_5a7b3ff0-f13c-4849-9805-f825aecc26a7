'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from '@/components/ui/dialog'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Loader, Upload } from 'lucide-react'
import Image from 'next/image'
import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'

import { Form, FormField } from '@/components/ui/form'
import { FormSelectField, FormInputField } from '@/components/form-fields'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { TeamInviteModal } from './team-invite-modal'
import { Separator } from './ui/separator'
import { DialogDescription } from '@radix-ui/react-dialog'
import { uploadFile } from '@/lib/minio-client'
import { AxiosError } from 'axios'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import workIcon from '../../public/assets/icons/workIcon.svg'
import { useAuthStore } from '@/store/auth.store'

type CreateWorkspaceModalProps = {
    open: boolean
    onOpenChange: (open: boolean) => void
}

type FileUploadResult = {
    success: boolean
    fileUrl?: string | null
}

const formSchema = z.object({
    workspaceName: z.string().min(1, 'Workspace Name is required'),
    companySize: z.string().min(1, 'Company size is required'),
    role: z.string().min(1, 'Role is required'),
})

export default function CreateWorkspaceModal({ open, onOpenChange }: CreateWorkspaceModalProps) {
    const [logoImage, setLogoImage] = useState<File | null>(null)
    const [inviteModalOpen, setInviteModalOpen] = useState(false)
    const [inviteLink, setInviteLink] = useState('')
    const [workspaceId, setWorkspaceId] = useState<number | null>(null)
    const { fetchMyDetails } = useAuthStore()

    const onAfterInviteSuccess = () => {
        setInviteLink('')
        setInviteModalOpen(false)
        toast.success('Invitation has been sent via email.')
        clearAllStates()
    }
    const clearAllStates = () => {
        setInviteLink('')
        setWorkspaceId(null)
    }

    useEffect(() => {
        if (workspaceId) {
            handleInvite('copy')
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [workspaceId])

    const inviteMutation = useMutation({
        mutationFn: async (data: { emails?: string[]; link?: string; action: string }) => {
            const url = endpoints.workspace.invite.replace(':id', workspaceId?.toString() || '')
            const response = await api.post(url, data)
            if (response.data.success && data.action === 'email') {
                onAfterInviteSuccess()
            }
            return response.data
        },
        onSuccess: (data) => {
            setInviteLink(data.invite_link)
        },
        onError: (error: AxiosError) => {
            const errorText = (error.response?.data as { error?: string })?.error
            axiosErrorToast(error, errorText || 'Failed to send Invite. Please try again.')
        },
    })

    const handleInvite = async (action: string, emails?: string[], link?: string) => {
        await inviteMutation.mutate({ emails, link, action })
        if (inviteMutation.isSuccess && !emails) setInviteModalOpen(true)
    }

    const handleInviteWithEmail = (emails: string[]) => {
        handleInvite('email', emails)
    }

    const companySizes = [
        { value: '1-10', label: '1-10 employees' },
        { value: '11-50', label: '11-50 employees' },
        { value: '51-200', label: '51-200 employees' },
        { value: '201-500', label: '201-500 employees' },
        { value: '501+', label: '501+ employees' },
    ]

    const companyRoles = [
        { value: 'founder', label: 'Founder / CEO' },
        { value: 'executive', label: 'Executive / C-level' },
        { value: 'manager', label: 'Manager / Team Lead' },
        { value: 'developer', label: 'Developer / Engineer' },
        { value: 'designer', label: 'Designer' },
        { value: 'marketing', label: 'Marketing / Sales' },
        { value: 'other', label: 'Other' },
    ]

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            workspaceName: '',
            companySize: '',
            role: '',
        },
    })

    const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            setLogoImage(e.target.files[0])
        }
    }

    const removeImage = () => setLogoImage(null)

    const mutation = useMutation({
        mutationFn: async (data: z.infer<typeof formSchema>) => {
            let result: FileUploadResult = { success: false, fileUrl: '' } // Initialize result
            let body: { name: string; plan: number; img_url?: string } = {
                name: data.workspaceName,
                plan: 0,
            }
            if (logoImage) {
                const formData = new FormData()
                formData.append('file', logoImage)
                result = await uploadFile(formData)
            }

            if (logoImage && !result.success) {
                throw new Error('Upload failed')
            }
            if (result.fileUrl) {
                body = { ...body, img_url: result.fileUrl }
            }

            const response = await api.post(endpoints.onboarding.createWorkSpace, body)
            setWorkspaceId(response.data.data.id)
            return response.data
        },

        onSuccess: async () => {
            toast.success('Workspace created successfully!')
            onOpenChange(false)
            form.reset()
            removeImage()
            fetchMyDetails()
            setInviteModalOpen(true)
        },
        onError: (error: AxiosError) => {
            axiosErrorToast(error, 'Failed to create workspace. Please try again.')
        },
    })

    const onSubmit = (values: z.infer<typeof formSchema>) => {
        const toastId = toast.loading('Creating workspace...')
        mutation.mutate(values, {
            onSettled: () => toast.dismiss(toastId),
        })
    }

    return (
        <>
            <Dialog
                modal
                open={open}
                onOpenChange={() => {
                    onOpenChange(false)
                    form.reset()
                    removeImage()
                }}>
                <DialogContent className="w-[475px]">
                    <DialogHeader>
                        <div className="border w-[36px] p-2 rounded-[10px]">
                            <Image src={workIcon} alt="Default Logo" width={26} height={26} className="mx-auto" />
                        </div>
                        <DialogTitle>Create Workspace</DialogTitle>
                        <DialogDescription className="text-[#71717A] text-xs">
                            Your ideas deserve a home. Start building together.
                        </DialogDescription>
                    </DialogHeader>
                    <Separator />
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            {/* Logo upload */}
                            <div className="w-full">
                                <div className="flex items-center space-x-3">
                                    <div className="relative w-12 h-12 rounded-full border overflow-hidden bg-[#08B38B0D] flex items-center justify-center">
                                        {logoImage && (
                                            <Image
                                                src={URL.createObjectURL(logoImage)}
                                                alt="Workspace Logo"
                                                width={48}
                                                height={48}
                                                className="object-cover"
                                            />
                                        )}
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium mb-2">Workspace Logo</label>

                                        <div className="flex items-center space-x-2">
                                            <Button variant="outline" size="sm" className="text-xs" asChild>
                                                <label>
                                                    Upload Image
                                                    <input
                                                        type="file"
                                                        className="hidden"
                                                        accept="image/*"
                                                        onChange={handleImageUpload} // Handle file selection
                                                    />
                                                    <Upload className="ml-1 h-3 w-3" />
                                                </label>
                                            </Button>
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                className="text-xs text-[#09090B]"
                                                onClick={removeImage}
                                                disabled={!logoImage}>
                                                Remove
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <FormField
                                control={form.control}
                                name="workspaceName"
                                render={({ field }) => (
                                    <FormInputField
                                        field={field}
                                        label="Workspace Name *"
                                        placeholder="Ex: Acme Inc"
                                        labelClassName="text-black"
                                    />
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="companySize"
                                render={({ field }) => (
                                    <FormSelectField
                                        field={field}
                                        label="How Large is your Company*"
                                        placeholder="Select company size"
                                        options={companySizes}
                                    />
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="role"
                                render={({ field }) => (
                                    <FormSelectField
                                        field={field}
                                        label="What is your role ? *"
                                        placeholder="Select your role"
                                        options={companyRoles}
                                    />
                                )}
                            />

                            <Button type="submit" className="w-full" disabled={mutation.isPending}>
                                {mutation.isPending ? <Loader className="animate-spin h-4 w-4 mr-2" /> : null}
                                Continue
                            </Button>
                        </form>
                    </Form>
                </DialogContent>
            </Dialog>
            <TeamInviteModal
                isOpen={inviteModalOpen}
                onOpenChange={() => {
                    setInviteModalOpen(false)
                    clearAllStates()
                }}
                showTrigger={false}
                defaultLink={inviteLink}
                onInvite={(emails) => {
                    handleInviteWithEmail(emails)
                }}
                isLoading={inviteMutation.isPending}
            />
        </>
    )
}
