'use client'

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Loader } from 'lucide-react'

interface ConfirmAlertProps {
    title?: string
    description?: string
    triggerText?: string
    onConfirm: () => Promise<void> | void // parent handles the action
    cancelText?: string
    confirmText?: string
    children?: React.ReactNode
    isAlertOpen?: boolean
    setIsAlertOpen?: (state: boolean) => void
    isLoading?: boolean // parent can control loading state
}

export function ConfirmAlert({
    title = 'Are you sure?',
    description = 'Please confirm this action.',
    onConfirm,
    cancelText = 'Cancel',
    confirmText = 'Confirm',
    children,
    isAlertOpen,
    setIsAlertOpen,
    isLoading = false,
}: ConfirmAlertProps) {
    const handleConfirm = async (e: React.MouseEvent) => {
        e.preventDefault()
        await onConfirm()
        if (setIsAlertOpen) setIsAlertOpen(false) // ✅ only runs on success
    }

    return (
        <AlertDialog open={isAlertOpen} onOpenChange={setIsAlertOpen}>
            <AlertDialogTrigger className="cursor-pointer" asChild>
                {children}
            </AlertDialogTrigger>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>{title}</AlertDialogTitle>
                    <AlertDialogDescription>{description}</AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel disabled={isLoading}>{cancelText}</AlertDialogCancel>
                    <AlertDialogAction onClick={handleConfirm} disabled={isLoading} className="min-w-[80px]">
                        {isLoading ? <Loader className="animate-spin w-3 h-3" /> : confirmText}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    )
}
