'use client'

import { ListChecks } from 'lucide-react'
import type { FC } from 'react'

// Props for the ChecklistChip component
interface ChecklistChipProps {
    steps: number // Number of steps in the checklist
    size?: 'sm' | 'md' | 'lg' // Size of the chip
}

/**
 * ChecklistChip component to display a styled chip for the number of steps in a checklist
 */
export const ChecklistChip: FC<ChecklistChipProps> = ({ steps, size = 'md' }) => {
    // Size classes mapping
    const sizeClasses = {
        sm: 'text-xs px-1 py-[2px]',
        md: 'text-sm px-2 py-1',
        lg: 'text-base px-3 py-2',
    }

    return (
        <div
            className={`inline-flex items-center gap-1 rounded-md border bg-[#F5F9F9] border-[#EEEDEE] ${sizeClasses[size]}`}
            data-steps={steps}>
            <div className="flex justify-center items-center bg-[#479696] rounded-full p-[3px]">
                <ListChecks size={14} className="text-white" />
            </div>
            {/* <div className="flex p-[1px] bg-[#F5F9F9] rounded-full w-[24px] h-[24px] justify-center items-center">
            </div> */}
            <span className="font-medium">{steps} steps</span>
        </div>
    )
}

export default ChecklistChip
