'use client'

import { Toolt<PERSON>, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip'
import { cn } from '@/lib/utils' // if you’re using a classNames utility

type ReusableTooltipProps = {
    children: React.ReactNode
    heading: string
    description: string | React.ReactNode
    className?: string
    side?: 'top' | 'bottom' | 'left' | 'right'
    sideOffset?: number
}

export const CustomReusableTooltip = ({
    children,
    heading,
    description,
    className,
    side = 'left',
    sideOffset = 4,
}: ReusableTooltipProps) => {
    return (
        <Tooltip>
            <TooltipTrigger asChild>{children}</TooltipTrigger>
            <TooltipContent
                className={cn('bg-[#FAFAFA] border border-[#D1D5DB] max-w-[280px] px-4 pt-2 space-y-2', className)}
                side={side}
                sideOffset={sideOffset}
                hideArrow>
                <p className="font-semibold text-xs text-black">{heading}</p>
                <div className="text-xs font-medium text-[#1F2937] text-justify">{description}</div>
            </TooltipContent>
        </Tooltip>
    )
}
