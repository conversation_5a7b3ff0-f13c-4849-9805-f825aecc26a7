import { AssigneeAvatar } from './assignee-avatar-with-fallback'
import { Crown, Pencil } from 'lucide-react'
import { Button } from './ui/button'
import { Loader } from 'lucide-react'

interface User {
    id: number
    email: string
    first_name: string
    last_name: string
    img_url: string
    cover_image_url: string
    user_type: number
}

interface UserProfileHeaderProps {
    user: User
    isUploading?: boolean
    handleImageUpload?: (e: React.ChangeEvent<HTMLInputElement>) => void
}

const UserProfileHeader = ({ user, isUploading, handleImageUpload }: UserProfileHeaderProps) => {
    return (
        <div className="relative">
            <div
                style={{
                    backgroundImage: user?.cover_image_url ? `url(${user?.cover_image_url})` : undefined,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                }}
                className="relative shadow-xs flex w-full h-[228px] justify-end bg-gradient-to-r from-slate-50 to-blue-500 rounded-md">
                {isUploading && (
                    <div className="absolute inset-0 bg-black/40 flex items-center justify-center z-10 rounded-md">
                        <Loader className="h-6 w-6 animate-spin text-white" />
                    </div>
                )}
                {handleImageUpload && (
                    <Button
                        className="flex items-center justify-center rounded-full bg-[#02061752] border-none mt-2 mr-2 z-20"
                        size="icon"
                        variant="outline">
                        <label className="cursor-pointer w-full flext items-center rounded-full py-2 justify-center ">
                            <input type="file" className="hidden" accept="image/*" onChange={handleImageUpload} />
                            <Pencil size={14} className="mx-auto" />
                        </label>
                    </Button>
                )}

                <div className="flex flex-col items-center absolute bottom-[-90%] left-18 transform -translate-x-1/2 -translate-y-1/2">
                    <div className="relative">
                        <AssigneeAvatar
                            assignee={user?.first_name + ' ' + user?.last_name}
                            imageUrl={user?.img_url}
                            className="border-2 border-white h-[128px] w-[128px]"
                            fallbackClassName="text-2xl"
                        />
                        {user?.user_type === 1 && (
                            <div className="absolute bottom-3 border border-[#EDE9FE] right-0 bg-[#FFFFFF] rounded-full p-1">
                                <Crown size={16} color="#7C3AED" />
                            </div>
                        )}
                    </div>
                    <div className="text-left pl-8">
                        <p className="text-[25px] text-[#1E293B] font-medium">{user?.first_name + ' ' + user?.last_name}</p>
                        <p className="text-sm text-[#475569]">{user?.email}</p>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default UserProfileHeader
