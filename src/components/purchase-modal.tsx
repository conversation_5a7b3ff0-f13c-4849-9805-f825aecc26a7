'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import NavigateToPricing from '@/app/(site)/site-components/navigate-to-pricing'

interface PurchaseModalProps {
    onClose?: () => void
    children?: React.ReactNode
    isFromPricingPage?: boolean
}

const messageOptions = [
    { quantity: 100, label: '100 user messages' },
    { quantity: 250, label: '250 user messages' },
    { quantity: 500, label: '500 user messages' },
    { quantity: 1000, label: '1,000 user messages' },
]

export function PurchaseModal({ onClose, isFromPricingPage, children }: PurchaseModalProps) {
    const [isOpen, setIsOpen] = useState(false)
    const [selectedQuantity, setSelectedQuantity] = useState(100)
    const pricePerMessage = 0.1
    const totalCost = selectedQuantity * pricePerMessage

    const handlePurchase = () => {
        // Handle purchase logic here
        console.log(`Purchasing ${selectedQuantity} messages for $${totalCost.toFixed(2)}`)
        if (onClose) {
            onClose()
        }
    }
    const handleOpenChange = (open: boolean) => {
        setIsOpen(open)
    }

    return (
        <Dialog open={isOpen} onOpenChange={handleOpenChange}>
            <DialogTrigger asChild>{children}</DialogTrigger>

            <DialogContent className="max-w-sm xl:max-w-lg">
                <DialogHeader>
                    <DialogTitle className="text-lg font-semibold">Purchase additional user messages</DialogTitle>
                </DialogHeader>

                <div className="space-y-6">
                    <DialogDescription className="text-sm text-muted-foreground">
                        Additional user messages expire after 12 months. It may take a few minutes for your new user messages to
                        become available.
                    </DialogDescription>

                    <div className="space-y-3">
                        <div>
                            <h3 className="font-semibold text-base mb-1">Select quantity</h3>
                            <p className="text-sm text-muted-foreground">US$0.10 per user message</p>
                        </div>

                        <div className="grid grid-cols-2 gap-2 border p-3 rounded-lg">
                            {messageOptions.map((option) => (
                                <Button
                                    key={option.quantity}
                                    variant={selectedQuantity === option.quantity ? 'default' : 'outline'}
                                    className={`h-12 text-sm font-medium ${
                                        selectedQuantity === option.quantity
                                            ? 'bg-[#08B38B] hover:bg-[#0f8569] text-white'
                                            : 'text-[#08B38B] border-[#bdeadf] hover:bg-green-50'
                                    }`}
                                    onClick={() => setSelectedQuantity(option.quantity)}>
                                    {option.label}
                                </Button>
                            ))}
                        </div>
                    </div>

                    <div className="space-y-2 pt-4 pb-2 border-b ">
                        <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Selected user messages</span>
                            <span>{selectedQuantity}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Price per user message</span>
                            <span>US${pricePerMessage.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between font-semibold">
                            <span>Total cost</span>
                            <span>US${totalCost.toFixed(2)}</span>
                        </div>
                    </div>

                    <div className="flex gap-3 pt-2 items-center justify-end">
                        <Button variant="outline" onClick={() => setIsOpen(false)}>
                            Cancel
                        </Button>
                        {isFromPricingPage ? (
                            <NavigateToPricing>
                                <Button className="  text-white">Purchase user messages</Button>
                            </NavigateToPricing>
                        ) : (
                            <Button className="  text-white" onClick={handlePurchase}>
                                Purchase user messages
                            </Button>
                        )}
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    )
}
