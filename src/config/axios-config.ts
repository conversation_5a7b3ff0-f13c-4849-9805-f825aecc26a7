import axios, { AxiosError, AxiosRequestConfig } from 'axios'
import endpoints from '@/services/api-endpoints'
import { useAuthStore } from '@/store/auth.store'

// ---------- Axios Instances ----------

export const api = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api/v1',
    headers: { 'Content-Type': 'application/json' },
})

export const apiAuth = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api/v1',
    withCredentials: true,
    headers: { 'Content-Type': 'application/json' },
})

// ---------- Types ----------

export interface ApiResponse<T = unknown> {
    data: T
    message: string
    status: number
}

interface RefreshResponse {
    accessToken: string
}

// Extend AxiosRequestConfig to track retries
interface CustomAxiosRequestConfig extends AxiosRequestConfig {
    _retry?: boolean
}

// ---------- Request Interceptor ----------

api.interceptors.request.use((config) => {
    const token = useAuthStore.getState().accessToken
    const userType = useAuthStore.getState().currentWorkspace?.user_type
    const workspaceId = useAuthStore.getState().currentWorkspace?.id

    // Add auth token
    if (token) {
        config.headers.Authorization = `Bearer ${token}`
    }

    // Add workspaceId header for non-GET requests
    const method = config.method?.toLowerCase()
    const methodsRequiringWorkspace = ['get', 'post', 'put', 'patch', 'delete']

    if (method && methodsRequiringWorkspace.includes(method) && userType) {
        config.headers['x-user-workspace-role'] = userType // 👈 Custom header
    }

    if (workspaceId) {
        config.headers['x-current-workspace-id'] = workspaceId // 👈 Custom header
    }

    return config
})

// ---------- Response Interceptor ----------

let refreshPromise: Promise<string | null> | null = null

api.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
        const originalRequest = error.config as CustomAxiosRequestConfig

        if (error.response?.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true

            if (!refreshPromise) {
                refreshPromise = apiAuth
                    .post<RefreshResponse>(endpoints.authentication.refresh)
                    .then((res) => {
                        const { accessToken } = res.data
                        useAuthStore.getState().setAccessToken(accessToken)
                        return accessToken
                    })
                    .catch(async () => {
                        console.warn('Refresh token invalid or expired. Logging out.')
                        try {
                            await useAuthStore.getState().logout()
                            window.location.href = '/sign-in'
                        } catch (logoutErr) {
                            console.error('Error during logout:', logoutErr)
                        }
                        return null
                    })
                    .finally(() => {
                        refreshPromise = null
                    })
            }

            const newAccessToken = await refreshPromise

            if (newAccessToken) {
                originalRequest.headers = {
                    ...originalRequest.headers,
                    Authorization: `Bearer ${newAccessToken}`,
                }
                return axios(originalRequest)
            }
        }

        return Promise.reject(error)
    },
)
