export const featureList = [
    {
        id: '1',
        title: 'Login Functionality',
        createdOn: '5/15/2024',
        type: 'folder' as const,
    },
    {
        id: '2',
        title: 'Sign up Functionality',
        createdOn: '5/15/2024',
        type: 'folder' as const,
    },
    {
        id: '3',
        title: 'Password Reset Flow',
        createdOn: '5/16/2024',
        type: 'folder' as const,
    },
    {
        id: '4',
        title: 'User Profile Settings',
        createdOn: '5/17/2024',
        type: 'folder' as const,
    },
    {
        id: '5',
        title: 'Email Verification',
        createdOn: '5/18/2024',
        type: 'folder' as const,
    },
    {
        id: '6',
        title: 'Two-Factor Authentication',
        createdOn: '5/19/2024',
        type: 'folder' as const,
    },
    {
        id: '7',
        title: 'Social Media Integration',
        createdOn: '5/20/2024',
        type: 'folder' as const,
    },
    {
        id: '8',
        title: 'User Dashboard',
        createdOn: '5/21/2024',
        type: 'folder' as const,
    },
]

export const dummyTaskData = [
    {
        taskNum: 1,
        id: 'task-001',
        category: 'Development',
        assignedRole: 'Senior Frontend Developer',
        description: 'Implement responsive design for the dashboard using React and Tailwind CSS',
        estimatedHours: 16,
        title: 'Dashboard Responsive Design',
        dependencies: ['task-003', 'task-005'],
    },
    {
        taskNum: 2,
        id: 'task-002',
        category: 'Development',
        assignedRole: 'Backend Engineer',
        description: 'Create API endpoints for user authentication and authorization',
        estimatedHours: 24,
        title: 'User Authentication System',
        dependencies: ['task-007'],
    },
    {
        taskNum: 3,
        id: 'task-003',
        category: 'Design',
        assignedRole: 'UI/UX Designer',
        description: 'Design wireframes and mockups for the new project management interface',
        estimatedHours: 12,
        title: 'Project Management UI Design',
        dependencies: [],
    },
    {
        taskNum: 4,
        id: 'task-004',
        category: 'DevOps',
        assignedRole: 'DevOps Engineer',
        description: 'Set up CI/CD pipeline for automated testing and deployment',
        estimatedHours: 20,
        title: 'Continuous Integration Setup',
        dependencies: ['task-002', 'task-006'],
    },
    {
        taskNum: 5,
        id: 'task-005',
        category: 'QA',
        assignedRole: 'Database Administrator',
        description: 'Optimize database queries and create performance indexes',
        estimatedHours: 15,
        title: 'Database Performance Optimization',
        dependencies: [],
    },
    {
        taskNum: 6,
        id: 'task-006',
        category: 'QA',
        assignedRole: 'QA Engineer',
        description: 'Develop comprehensive test cases for critical user flows',
        estimatedHours: 18,
        title: 'User Flow Test Suite',
        dependencies: ['task-001', 'task-002'],
    },
    {
        taskNum: 7,
        id: 'task-007',
        category: 'DevOps',
        assignedRole: 'Security Specialist',
        description: 'Implement security best practices and conduct vulnerability assessment',
        estimatedHours: 25,
        title: 'Security Audit and Hardening',
        dependencies: [],
    },
]

// Dummy data for departments (only ID and name)
interface DepartmentData {
    id: string
    name: string
}

export const departments: Record<string, DepartmentData> = {
    design: {
        id: 'design',
        name: 'Design',
    },
    development: {
        id: 'development',
        name: 'Development',
    },
    qa: {
        id: 'qa',
        name: 'QA',
    },
    devops: {
        id: 'devops',
        name: 'Devops',
    },
}
