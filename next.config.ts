import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
    reactStrictMode: false,
    devIndicators: false,
    experimental: {
        serverActions: {
            bodySizeLimit: '10mb',
        },
        webVitalsAttribution: ['CLS', 'LCP', 'FID', 'FCP', 'TTFB'],
    },
    images: {
        remotePatterns: [
            {
                protocol: 'http',
                hostname: '**************',
                port: '9000',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'minio.raydian.ai',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'lh3.googleusercontent.com',
                pathname: '/**',
            },
        ],
        qualities: [75, 80, 85, 90, 95],
    },
}

export default nextConfig
