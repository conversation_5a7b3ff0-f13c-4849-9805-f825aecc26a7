username: GitHub-CI
icon_url: https://octodex.github.com/images/mona-the-rivetertocat.png
pretext: '🚀 *{{workflow}}* started by *{{actor}}*'
title: GitHub Actions Deployment
title_link: '{{workflowRunUrl}}'
text: |
    *Repository:* <{{repositoryUrl}}|{{repositoryName}}>
    *Branch:* <{{refUrl}}|`{{ref}}`>
    *Commit:* `{{shortSha}}` - {{commitMessage}}
    *Status:* {{jobStatus}} {{icon jobStatus}}

fields:
    - title: Workflow
      value: '<{{workflowRunUrl}}|{{workflow}}>'
      short: true
    - title: Trigger
      value: '{{eventName}}'
      short: true
    - title: Job
      value: '{{jobName}}'
      short: true
    - title: Run ID
      value: '{{runId}}'
      short: true

footer: '<{{repositoryUrl}}|{{repositoryName}}> • Run ID: {{runId}}'
colors:
    success: '#2eb886'
    failure: '#e01e5a'
    cancelled: '#808080'
icons:
    success: ':white_check_mark:'
    failure: ':x:'
    cancelled: ':no_entry:'
    default: ':grey_question:'
